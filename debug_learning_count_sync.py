#!/usr/bin/env python3
"""
Debug Learning Count Synchronization Issue
Investigates why deletion count doesn't sync with console logs
"""

import sys
sys.path.append('.')

def test_learning_count_sync():
    """Test the learning count synchronization issue"""
    print("🔍 DEBUGGING LEARNING COUNT SYNCHRONIZATION")
    print("=" * 60)
    
    try:
        from enhanced_adaptive_learning_system import enhanced_learning_system
        from prediction_tracker import PredictionTracker
        
        # Get current counts
        tracker = PredictionTracker()
        
        print("📊 CURRENT SYSTEM COUNTS:")
        print(f"   Main system total predictions: {len(tracker.predictions)}")
        
        ai_predictions = [p for p in tracker.predictions if getattr(p, 'is_ai_prediction', False)]
        print(f"   Main system AI predictions: {len(ai_predictions)}")
        
        completed_ai = [p for p in ai_predictions if p.actual_winner and getattr(p, 'match_status', None) == 'completed']
        print(f"   Main system completed AI predictions: {len(completed_ai)}")
        
        print(f"   Enhanced system total predictions: {len(enhanced_learning_system.contextual_predictions)}")
        
        eligible_enhanced = enhanced_learning_system.get_learning_eligible_predictions()
        print(f"   Enhanced system learning-eligible: {len(eligible_enhanced)}")
        
        print("\n🔍 ENHANCED SYSTEM PREDICTION DETAILS:")
        for i, pred in enumerate(enhanced_learning_system.contextual_predictions):
            eligible = enhanced_learning_system.is_prediction_eligible_for_learning(pred)
            print(f"   {i+1}. ID: {pred.prediction_id[:20]}...")
            print(f"      Score: {pred.score}, Set: {pred.set_number}")
            print(f"      Has outcome: {pred.actual_winner is not None}")
            print(f"      Match status: {getattr(pred, 'match_status', 'unknown')}")
            print(f"      Is AI prediction: {getattr(pred, 'is_ai_prediction', 'unknown')}")
            print(f"      Eligible for learning: {eligible}")
            print()
        
        print("🔍 CHECKING FOR SYNC ISSUES:")
        
        # Check if there are predictions with outcomes but wrong status
        wrong_status = []
        for pred in enhanced_learning_system.contextual_predictions:
            if pred.actual_winner and getattr(pred, 'match_status', None) != 'completed':
                wrong_status.append(pred)
        
        if wrong_status:
            print(f"   ⚠️ Found {len(wrong_status)} predictions with outcomes but wrong status:")
            for pred in wrong_status:
                print(f"      {pred.prediction_id}: status='{getattr(pred, 'match_status', 'unknown')}', outcome='{pred.actual_winner}'")
        else:
            print("   ✅ All predictions with outcomes have correct status")
        
        # Check for predictions without is_ai_prediction flag
        missing_ai_flag = []
        for pred in enhanced_learning_system.contextual_predictions:
            if not hasattr(pred, 'is_ai_prediction'):
                missing_ai_flag.append(pred)
        
        if missing_ai_flag:
            print(f"   ⚠️ Found {len(missing_ai_flag)} predictions missing is_ai_prediction flag")
        else:
            print("   ✅ All predictions have is_ai_prediction flag")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during sync test: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_sync_issues():
    """Fix any detected synchronization issues"""
    print("\n🔧 FIXING SYNCHRONIZATION ISSUES")
    print("=" * 60)
    
    try:
        from enhanced_adaptive_learning_system import enhanced_learning_system
        
        fixes_made = 0
        
        # Fix 1: Ensure predictions with outcomes have completed status
        for pred in enhanced_learning_system.contextual_predictions:
            if pred.actual_winner and getattr(pred, 'match_status', None) != 'completed':
                pred.match_status = 'completed'
                fixes_made += 1
                print(f"   ✅ Fixed status for {pred.prediction_id}")
        
        # Fix 2: Ensure all predictions have is_ai_prediction flag
        for pred in enhanced_learning_system.contextual_predictions:
            if not hasattr(pred, 'is_ai_prediction'):
                pred.is_ai_prediction = True  # Default for enhanced system
                fixes_made += 1
                print(f"   ✅ Added AI flag for {pred.prediction_id}")
        
        if fixes_made > 0:
            enhanced_learning_system.save_contextual_predictions()
            print(f"\n✅ Applied {fixes_made} fixes and saved data")
            
            # Show updated count
            eligible_count = len(enhanced_learning_system.get_learning_eligible_predictions())
            print(f"📊 Updated learning-eligible count: {eligible_count}")
        else:
            print("✅ No fixes needed - system is synchronized")
        
        return fixes_made
        
    except Exception as e:
        print(f"❌ Error during sync fix: {e}")
        import traceback
        traceback.print_exc()
        return 0

if __name__ == "__main__":
    print("🚀 LEARNING COUNT SYNC DEBUGGER")
    print("=" * 60)
    
    # Test current state
    if test_learning_count_sync():
        # Fix any issues
        fixes = fix_sync_issues()
        
        if fixes > 0:
            print("\n🔄 RE-TESTING AFTER FIXES:")
            test_learning_count_sync()
    
    print("\n✅ Debug session complete")
