#!/usr/bin/env python3
"""
Test the data format mismatch in deletion
"""

def test_deletion_format_issue():
    """Test the data format mismatch causing deletion failures"""
    print("🔍 TESTING DELETION FORMAT ISSUE")
    print("=" * 50)
    
    try:
        from enhanced_adaptive_learning_system import enhanced_learning_system
        from prediction_tracker import PredictionTracker
        
        # Check current data formats
        print("\n1️⃣ Data Format Analysis:")
        
        enhanced_predictions = enhanced_learning_system.contextual_predictions
        print(f"   Enhanced system predictions: {len(enhanced_predictions)}")
        
        if enhanced_predictions:
            pred = enhanced_predictions[0]
            print(f"   Enhanced prediction score type: {type(pred.score)} = {pred.score}")
            print(f"   Enhanced prediction score value: {repr(pred.score)}")
        
        # Create a test prediction like the main tracker would
        main_tracker = PredictionTracker()
        print(f"   Main tracker predictions: {len(main_tracker.predictions)}")
        
        # Simulate what happens when deleting
        print("\n2️⃣ Deletion Simulation:")
        
        if enhanced_predictions:
            test_pred = enhanced_predictions[0]
            print(f"   Testing deletion of: {test_pred.score}")
            
            # Test different score formats that might come from main tracker
            test_formats = [
                test_pred.score,  # Original format [3, 3]
                tuple(test_pred.score),  # Convert to tuple (3, 3)
                (test_pred.score[0], test_pred.score[1]),  # Explicit tuple
                f"({test_pred.score[0]},{test_pred.score[1]})",  # String format
            ]
            
            print(f"   Testing different score formats:")
            for i, test_score in enumerate(test_formats):
                print(f"     Format {i+1}: {type(test_score)} = {repr(test_score)}")
                
                # Test if it would match
                try:
                    is_match = (test_pred.score == test_score)
                    print(f"       Match result: {is_match}")
                except Exception as e:
                    print(f"       Match error: {e}")
        
        # Test the actual deletion method
        print("\n3️⃣ Testing Actual Deletion Method:")
        
        if enhanced_predictions:
            test_pred = enhanced_predictions[0]
            
            # Test with the exact format that would come from main tracker
            # Main tracker stores score as tuple, enhanced stores as list
            main_score_format = tuple(test_pred.score)  # Convert [3,3] to (3,3)
            
            print(f"   Original enhanced score: {type(test_pred.score)} = {test_pred.score}")
            print(f"   Main tracker format: {type(main_score_format)} = {main_score_format}")
            print(f"   Would they match? {test_pred.score == main_score_format}")
            
            # This is the problem! List [3,3] != Tuple (3,3)
            
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_deletion_format_issue()
