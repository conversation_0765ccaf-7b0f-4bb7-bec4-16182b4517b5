#!/usr/bin/env python3
"""
Test script to verify that BP and AD are properly excluded from player code extraction.
Tests the specific case mentioned by the user where <PERSON><PERSON> was being replaced with <PERSON>.
"""

def extract_player_codes_from_data_test(match_data_text):
    """
    Test version of the enhanced extract_player_codes_from_data method.
    This replicates the fixed logic from tennis.py.
    """
    try:
        if not match_data_text:
            return None

        lines = match_data_text.split('\n')
        found_codes = []

        # Look for player codes in various formats
        for line in lines:
            line = line.strip()

            # Skip empty lines
            if not line:
                continue

            extracted_code = None

            # Pattern 1: Prefixed codes like "I.MON", "J.MON" (single letter + period + code)
            if len(line) >= 3 and '.' in line:
                parts = line.split('.')
                if len(parts) == 2 and len(parts[0]) == 1 and parts[0].isalpha():
                    # Extract the code part after the period
                    code_part = parts[1].strip()
                    if len(code_part) >= 2 and code_part.isalpha() and code_part.isupper():
                        # Keep the full prefixed code (e.g., "I.MON" instead of just "M<PERSON>")
                        extracted_code = line

            # Pattern 2: Plain codes (2+ letters, all uppercase)
            elif len(line) >= 2 and line.isalpha() and line.isupper():
                extracted_code = line

            # Validate and add the extracted code
            if extracted_code:
                # Avoid common non-player codes and tennis terms (FIXED VERSION)
                excluded_codes = {'SET', 'WIN', 'END', 'ADV', 'ACE', 'UNF', 'NET', 'OUT', 'DBF',
                                'TIED', 'GAME', 'MATCH', 'POINT', 'SERVE', 'RETURN', 'BREAK', 'HOLD',
                                'BP', 'AD', 'DEUCE', 'LOVE', 'ALL', 'FAULT', 'LET', 'WINNER', 'ERROR'}

                if extracted_code not in excluded_codes:
                    found_codes.append(extracted_code)
                    # Stop after finding 2 codes (allow duplicates for same player codes)
                    if len(found_codes) >= 2:
                        break

        # Return the first two codes found (Player 1, Player 2)
        if len(found_codes) >= 2:
            return found_codes[:2]
        elif len(found_codes) == 1:
            return [found_codes[0], None]
        else:
            return None

    except Exception as e:
        print(f"Error extracting player codes from data: {e}")
        return None

def test_bp_ad_exclusion():
    """Test that BP and AD are properly excluded from player code extraction"""
    
    print("🎾 Testing BP/AD Exclusion Fix")
    print("=" * 60)
    
    # User's specific example where TIE was being replaced with BP
    user_example = """0
-
0
tied
NIS
TIE
0
15
0
30
0
BP
40
15
BP
40
30
BP
40
40
40
AD
40
1
-
0
Kei Nishikori
NIS
TIE
15
0
30
0
30
15
30
30
30
40"""
    
    print("📋 User's Example Data:")
    print("Expected codes: NIS, TIE")
    print("Problem: TIE was being replaced with BP")
    print()
    
    result = extract_player_codes_from_data_test(user_example)
    print(f"✅ Extracted codes: {result}")
    
    expected = ["NIS", "TIE"]
    if result == expected:
        print("✅ PASS: Correctly extracted NIS and TIE, ignored BP and AD")
    else:
        print(f"❌ FAIL: Expected {expected}, got {result}")
    
    print("\n" + "-" * 40)
    
    # Test case with BP and AD mixed with valid codes
    test_case_2 = """0
-
0
tied
ALB
ZVE
15
0
30
0
BP
40
15
AD
40
30
BP
40
40
1
-
0
Alexander Zverev
ALB
ZVE"""
    
    print("\n📋 Test Case 2: BP/AD mixed with valid codes")
    print("Expected codes: ALB, ZVE")
    print("Should ignore: BP, AD")
    
    result_2 = extract_player_codes_from_data_test(test_case_2)
    print(f"✅ Extracted codes: {result_2}")
    
    expected_2 = ["ALB", "ZVE"]
    if result_2 == expected_2:
        print("✅ PASS: Correctly extracted ALB and ZVE, ignored BP and AD")
    else:
        print(f"❌ FAIL: Expected {expected_2}, got {result_2}")
    
    print("\n" + "-" * 40)
    
    # Test case with only tennis terms (should return None)
    test_case_3 = """0
-
0
tied
BP
AD
15
0
30
0
BP
40
15
AD
40
30
DEUCE
40
40
1
-
0"""
    
    print("\n📋 Test Case 3: Only tennis terms")
    print("Expected codes: None (no valid player codes)")
    print("Should ignore: BP, AD, DEUCE")
    
    result_3 = extract_player_codes_from_data_test(test_case_3)
    print(f"✅ Extracted codes: {result_3}")
    
    if result_3 is None:
        print("✅ PASS: Correctly ignored all tennis terms, returned None")
    else:
        print(f"❌ FAIL: Expected None, got {result_3}")

def test_comprehensive_exclusions():
    """Test all the excluded tennis terms"""
    
    print("\n🎾 Testing Comprehensive Tennis Term Exclusions")
    print("=" * 60)
    
    excluded_terms = ['SET', 'WIN', 'END', 'ADV', 'ACE', 'UNF', 'NET', 'OUT', 'DBF',
                     'TIED', 'GAME', 'MATCH', 'POINT', 'SERVE', 'RETURN', 'BREAK', 'HOLD',
                     'BP', 'AD', 'DEUCE', 'LOVE', 'ALL', 'FAULT', 'LET', 'WINNER', 'ERROR']
    
    # Create test data with valid codes mixed with excluded terms
    test_data = """0
-
0
tied
FED
NAD
SET
WIN
BP
AD
DEUCE
ACE
NET
OUT
15
0
30
0
40
0
1
-
0"""
    
    print("📋 Test Data with valid codes (FED, NAD) and excluded terms")
    print("Expected codes: FED, NAD")
    print(f"Should ignore: {', '.join(excluded_terms)}")
    
    result = extract_player_codes_from_data_test(test_data)
    print(f"✅ Extracted codes: {result}")
    
    expected = ["FED", "NAD"]
    if result == expected:
        print("✅ PASS: Correctly extracted valid codes and ignored all tennis terms")
    else:
        print(f"❌ FAIL: Expected {expected}, got {result}")
    
    print("\n" + "=" * 60)
    print("🎯 Summary:")
    print("✅ BP and AD are now properly excluded from player code extraction")
    print("✅ The auto-fill logic will no longer confuse tennis terms with player codes")
    print("✅ TIE will be preserved as a valid player code")
    print("✅ All common tennis terms are excluded from extraction")

if __name__ == "__main__":
    test_bp_ad_exclusion()
    test_comprehensive_exclusions()
