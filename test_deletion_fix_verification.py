#!/usr/bin/env python3
"""
Test the deletion fix for format mismatch
"""

def test_deletion_fix():
    """Test that the deletion fix works for format mismatch"""
    print("🧪 TESTING DELETION FIX")
    print("=" * 50)
    
    try:
        from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem
        
        # Create a fresh instance to test the fix
        enhanced_system = EnhancedAdaptiveLearningSystem()
        
        print(f"\n1️⃣ Current State:")
        print(f"   Enhanced system predictions: {len(enhanced_system.contextual_predictions)}")
        
        if enhanced_system.contextual_predictions:
            test_pred = enhanced_system.contextual_predictions[0]
            print(f"   Test prediction: {test_pred.score} (type: {type(test_pred.score)})")
            
            # Test deletion with tuple format (what main tracker would send)
            tuple_score = tuple(test_pred.score)
            print(f"   Testing deletion with tuple: {tuple_score} (type: {type(tuple_score)})")
            
            # Test the deletion method (dry run - don't actually delete)
            print(f"\n2️⃣ Testing Deletion Logic:")
            
            # Simulate the deletion matching logic
            from datetime import datetime
            target_timestamp = datetime.fromisoformat(test_pred.timestamp.replace('Z', '+00:00'))
            
            # Test the new matching logic
            pred_score = tuple(test_pred.score) if isinstance(test_pred.score, list) else test_pred.score
            target_score = tuple(tuple_score) if isinstance(tuple_score, list) else tuple_score
            
            print(f"   Pred score (normalized): {pred_score} (type: {type(pred_score)})")
            print(f"   Target score (normalized): {target_score} (type: {type(target_score)})")
            print(f"   Scores match: {pred_score == target_score}")
            
            # Test actual deletion method
            print(f"\n3️⃣ Testing Actual Deletion Method:")
            initial_count = len(enhanced_system.contextual_predictions)
            
            deleted_count = enhanced_system.delete_prediction_by_criteria(
                tuple_score, test_pred.set_number, test_pred.timestamp
            )
            
            final_count = len(enhanced_system.contextual_predictions)
            
            print(f"   Initial count: {initial_count}")
            print(f"   Deleted count: {deleted_count}")
            print(f"   Final count: {final_count}")
            
            if deleted_count > 0:
                print("   ✅ Deletion successful! Format mismatch fixed.")
                
                # Restore the prediction for next test
                enhanced_system.load_contextual_predictions()
                print("   🔄 Restored data for next test")
            else:
                print("   ❌ Deletion failed - fix didn't work")
                
        else:
            print("   No predictions to test with")
            
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_deletion_fix()
