#!/usr/bin/env python3
"""
Test Tournament Level Fix
Verify that tournament levels are correctly extracted from prediction metadata.
"""

import sys
sys.path.append('.')

def test_tournament_level_extraction():
    """Test that tournament levels are correctly extracted from predictions"""
    print("🧪 TESTING TOURNAMENT LEVEL EXTRACTION")
    print("=" * 50)
    
    try:
        from prediction_tracker import PredictionTracker
        
        # Load current predictions
        tracker = PredictionTracker()
        ai_predictions = [p for p in tracker.predictions if getattr(p, 'is_ai_prediction', False)]
        
        print(f"📊 Found {len(ai_predictions)} AI predictions")
        
        if not ai_predictions:
            print("⚠️ No AI predictions found to test")
            return True
        
        # Test the extraction logic for each prediction
        for i, pred in enumerate(ai_predictions, 1):
            print(f"\n🔍 Testing prediction {i}:")
            print(f"   Players: {pred.player1_name} vs {pred.player2_name}")
            
            # Simulate the extraction logic from tennis.py
            tournament_level = 'Mixed'  # Default fallback
            source = 'default'
            
            # Check prediction's learning metadata first
            if hasattr(pred, 'learning_metadata') and pred.learning_metadata:
                extracted_level = pred.learning_metadata.get('tournament_level', None)
                if extracted_level:
                    tournament_level = extracted_level
                    source = 'learning_metadata'
            
            # Check prediction's context factors as backup
            elif hasattr(pred, 'context_factors') and pred.context_factors:
                match_context = pred.context_factors.get('match_context', {})
                extracted_level = match_context.get('tournament_level', None)
                if extracted_level:
                    tournament_level = extracted_level
                    source = 'context_factors'
            
            print(f"   ✅ Tournament Level: {tournament_level} (from {source})")
            
            # Verify it's not defaulting to Mixed when data is available
            if tournament_level == 'Mixed' and source == 'default':
                print(f"   ⚠️ Warning: Defaulted to Mixed - check if metadata is available")
                if hasattr(pred, 'learning_metadata'):
                    print(f"      Learning metadata: {pred.learning_metadata}")
                if hasattr(pred, 'context_factors'):
                    print(f"      Context factors: {pred.context_factors}")
        
        print(f"\n✅ Tournament level extraction test completed")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_enhanced_learning_integration():
    """Test the enhanced learning integration with correct tournament levels"""
    print("\n🧪 TESTING ENHANCED LEARNING INTEGRATION")
    print("=" * 50)
    
    try:
        from learning_system_integration import learning_integrator
        
        # Get integration status
        status = learning_integrator.get_integration_status()
        
        print(f"📊 Integration Status:")
        print(f"   Total predictions: {status.get('total_predictions', 0)}")
        print(f"   Segments with weights: {status.get('segments_with_weights', 0)}")
        
        # Check segment breakdown
        learning_status = status.get('learning_system_status', {})
        segment_stats = learning_status.get('segment_stats', {})
        
        print(f"\n📋 Segment Breakdown:")
        for segment, stats in segment_stats.items():
            print(f"   {segment}: {stats.get('total_predictions', 0)} predictions")
        
        # Verify ATP_Clay segment exists (from your dashboard screenshot)
        if 'ATP_Clay' in segment_stats:
            atp_clay_stats = segment_stats['ATP_Clay']
            print(f"\n✅ ATP_Clay segment found:")
            print(f"   Total predictions: {atp_clay_stats.get('total_predictions', 0)}")
            print(f"   Completed: {atp_clay_stats.get('completed_predictions', 0)}")
        else:
            print(f"\n⚠️ ATP_Clay segment not found in current data")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 TOURNAMENT LEVEL FIX VERIFICATION")
    print("=" * 60)
    
    test1_passed = test_tournament_level_extraction()
    test2_passed = test_enhanced_learning_integration()
    
    print(f"\n📋 TEST RESULTS:")
    print(f"   Tournament Level Extraction: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"   Enhanced Learning Integration: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"The tournament level extraction fix should resolve the 'Mixed' issue.")
    else:
        print(f"\n⚠️ SOME TESTS FAILED")
        print(f"Please review the output above for details.")


if __name__ == "__main__":
    main()
