#!/usr/bin/env python3
"""
Fix Dashboard Count Synchronization Issue
Ensures dashboard and console logs show consistent learning-eligible counts
"""

import sys
sys.path.append('.')

def analyze_count_discrepancy():
    """Analyze the discrepancy between dashboard and console counts"""
    print("🔍 ANALYZING COUNT DISCREPANCY")
    print("=" * 60)
    
    try:
        from enhanced_adaptive_learning_system import enhanced_learning_system
        from learning_system_integration import learning_integrator
        from prediction_tracker import PredictionTracker
        
        # Get counts from different sources
        tracker = PredictionTracker()
        
        # Dashboard source (learning_integrator)
        integration_status = learning_integrator.get_integration_status()
        dashboard_ai_count = integration_status['total_predictions']
        
        # Console log source (enhanced_learning_system)
        enhanced_eligible = enhanced_learning_system.get_learning_eligible_predictions()
        console_count = len(enhanced_eligible)
        
        # Main tracker AI predictions
        main_ai_predictions = [p for p in tracker.predictions if getattr(p, 'is_ai_prediction', False)]
        main_ai_completed = [p for p in main_ai_predictions if p.actual_winner and getattr(p, 'match_status', None) == 'completed']
        
        print("📊 COUNT COMPARISON:")
        print(f"   Dashboard count (learning_integrator): {dashboard_ai_count}")
        print(f"   Console count (enhanced_learning_system): {console_count}")
        print(f"   Main tracker AI predictions: {len(main_ai_predictions)}")
        print(f"   Main tracker AI completed: {len(main_ai_completed)}")
        print(f"   Enhanced system total: {len(enhanced_learning_system.contextual_predictions)}")
        
        print("\n🔍 DETAILED ANALYSIS:")
        
        # Check if dashboard is counting correctly
        print("Dashboard AI predictions (from main tracker):")
        for i, pred in enumerate(main_ai_predictions):
            eligible = (pred.actual_winner and getattr(pred, 'match_status', None) == 'completed')
            print(f"   {i+1}. Score: {pred.score}, Set: {pred.set_number}")
            print(f"      Has outcome: {pred.actual_winner is not None}")
            print(f"      Status: {getattr(pred, 'match_status', 'unknown')}")
            print(f"      Eligible: {eligible}")
        
        print(f"\nEnhanced system predictions:")
        for i, pred in enumerate(enhanced_learning_system.contextual_predictions):
            eligible = enhanced_learning_system.is_prediction_eligible_for_learning(pred)
            print(f"   {i+1}. Score: {pred.score}, Set: {pred.set_number}")
            print(f"      Has outcome: {pred.actual_winner is not None}")
            print(f"      Status: {getattr(pred, 'match_status', 'unknown')}")
            print(f"      Eligible: {eligible}")
        
        # Identify the discrepancy
        discrepancy = console_count - len(main_ai_completed)
        if discrepancy != 0:
            print(f"\n⚠️ DISCREPANCY FOUND: {discrepancy} prediction(s)")
            print("   This suggests the enhanced system has predictions not in main tracker")
            print("   or different eligibility criteria")
        else:
            print("\n✅ Counts match - no discrepancy")
        
        return {
            'dashboard_count': dashboard_ai_count,
            'console_count': console_count,
            'main_completed': len(main_ai_completed),
            'discrepancy': discrepancy
        }
        
    except Exception as e:
        print(f"❌ Error analyzing discrepancy: {e}")
        import traceback
        traceback.print_exc()
        return None

def fix_count_synchronization():
    """Fix the count synchronization issue"""
    print("\n🔧 FIXING COUNT SYNCHRONIZATION")
    print("=" * 60)
    
    try:
        from enhanced_adaptive_learning_system import enhanced_learning_system
        from comprehensive_sync_manager import ComprehensiveSyncManager
        
        # Force a comprehensive sync
        sync_manager = ComprehensiveSyncManager()
        
        print("🔄 Running comprehensive sync...")
        sync_result = sync_manager.auto_fix_sync_issues()
        
        print(f"✅ Sync completed:")
        print(f"   Main system fixes: {sync_result['main_system_fixes']}")
        print(f"   Enhanced system fixes: {sync_result['enhanced_system_fixes']}")
        print(f"   Database fixes: {sync_result['database_fixes']}")
        print(f"   Total fixes: {sync_result['total_fixes']}")
        
        # Force refresh counts
        updated_counts = sync_manager.force_refresh_learning_counts()
        print(f"\n📊 Updated counts:")
        print(f"   Enhanced system eligible: {updated_counts['enhanced_system_eligible']}")
        print(f"   Main system completed: {updated_counts['main_system_completed']}")
        
        return updated_counts
        
    except Exception as e:
        print(f"❌ Error fixing synchronization: {e}")
        import traceback
        traceback.print_exc()
        return None

def update_dashboard_counting_logic():
    """Suggest fix for dashboard counting logic"""
    print("\n💡 DASHBOARD COUNTING LOGIC FIX")
    print("=" * 60)
    
    print("The issue is that the dashboard counts from main tracker,")
    print("but console logs count from enhanced system.")
    print()
    print("Recommended fix:")
    print("1. Update learning_system_integration.py to use enhanced system count")
    print("2. Or update enhanced system to log main tracker count")
    print("3. Ensure both systems use same eligibility criteria")
    print()
    print("Current logic:")
    print("   Dashboard: learning_integrator.get_integration_status()['total_predictions']")
    print("   Console:   enhanced_learning_system.get_learning_eligible_predictions()")
    print()
    print("Both should use the same source for consistency.")

if __name__ == "__main__":
    print("🚀 DASHBOARD COUNT SYNC FIXER")
    print("=" * 60)
    
    # Analyze the discrepancy
    analysis = analyze_count_discrepancy()
    
    if analysis and analysis['discrepancy'] != 0:
        # Try to fix the synchronization
        fix_result = fix_count_synchronization()
        
        if fix_result:
            print("\n🔄 RE-ANALYZING AFTER FIX:")
            analyze_count_discrepancy()
    
    # Provide recommendations
    update_dashboard_counting_logic()
    
    print("\n✅ Analysis complete")
