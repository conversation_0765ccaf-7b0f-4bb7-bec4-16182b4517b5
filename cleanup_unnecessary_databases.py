#!/usr/bin/env python3
"""
Cleanup Unnecessary Database Files
Safely removes redundant and unused database files after code has been updated.
"""

import os
import sys
import shutil
from pathlib import Path
from datetime import datetime


def check_file_locks():
    """Check if any processes are using the database files"""
    print("🔍 CHECKING FOR FILE LOCKS")
    print("=" * 50)
    
    files_to_check = [
        "enhanced_learning_data/enhanced_learning.db",
        "learning_data/learning_database.db"
    ]
    
    locked_files = []
    
    for file_path in files_to_check:
        file_obj = Path(file_path)
        if file_obj.exists():
            try:
                # Try to open the file in write mode to check if it's locked
                with open(file_obj, 'r+b') as f:
                    pass
                print(f"✅ {file_path} - Not locked")
            except (PermissionError, OSError) as e:
                print(f"🔒 {file_path} - LOCKED ({e})")
                locked_files.append(file_path)
        else:
            print(f"📁 {file_path} - Not found")
    
    return locked_files


def create_final_backup():
    """Create a final backup before deletion"""
    print("\n💾 CREATING FINAL BACKUP")
    print("=" * 50)
    
    backup_dir = Path(f"final_cleanup_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    backup_dir.mkdir(exist_ok=True)
    
    files_to_backup = [
        "enhanced_learning_data/enhanced_learning.db",
        "learning_data/learning_database.db"
    ]
    
    backed_up = []
    for file_path in files_to_backup:
        source = Path(file_path)
        if source.exists():
            dest = backup_dir / source.name
            try:
                shutil.copy2(source, dest)
                backed_up.append(file_path)
                print(f"✅ Backed up: {file_path}")
            except Exception as e:
                print(f"❌ Failed to backup {file_path}: {e}")
        else:
            print(f"📁 Not found: {file_path}")
    
    print(f"\n📁 Final backup created in: {backup_dir}")
    return backup_dir, backed_up


def remove_redundant_enhanced_db():
    """Remove the redundant enhanced learning SQLite database"""
    print("\n🗑️ REMOVING REDUNDANT ENHANCED LEARNING DATABASE")
    print("=" * 50)
    
    db_file = Path("enhanced_learning_data/enhanced_learning.db")
    json_file = Path("enhanced_learning_data/contextual_predictions.json")
    
    if not db_file.exists():
        print("📁 Enhanced SQLite database not found - already removed")
        return True
    
    if not json_file.exists():
        print("⚠️ JSON file not found - keeping SQLite as backup")
        return False
    
    try:
        # Verify JSON has data
        import json
        with open(json_file, 'r') as f:
            data = json.load(f)
            if len(data) == 0:
                print("⚠️ JSON file is empty - keeping SQLite as backup")
                return False
            
            print(f"✅ JSON file has {len(data)} records - safe to remove SQLite")
        
        # Remove the SQLite database
        db_file.unlink()
        print(f"🗑️ Successfully removed: {db_file}")
        return True
        
    except Exception as e:
        print(f"❌ Error removing enhanced database: {e}")
        return False


def remove_unused_adaptive_db():
    """Remove the completely unused adaptive learning database"""
    print("\n🗑️ REMOVING UNUSED ADAPTIVE LEARNING DATABASE")
    print("=" * 50)
    
    db_file = Path("learning_data/learning_database.db")
    
    if not db_file.exists():
        print("📁 Adaptive database not found - already removed")
        return True
    
    try:
        # Double-check it's really empty
        import sqlite3
        with sqlite3.connect(db_file) as conn:
            cursor = conn.cursor()
            
            # Check all tables for data
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            total_records = 0
            for table_name, in tables:
                if table_name != 'sqlite_sequence':
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    total_records += count
            
            if total_records > 0:
                print(f"⚠️ Database has {total_records} records - NOT removing")
                return False
            
            print("✅ Confirmed: Database is completely empty")
        
        # Remove the database
        db_file.unlink()
        print(f"🗑️ Successfully removed: {db_file}")
        return True
        
    except Exception as e:
        print(f"❌ Error removing adaptive database: {e}")
        return False


def cleanup_empty_directories():
    """Remove empty directories if they exist"""
    print("\n🧹 CLEANING UP EMPTY DIRECTORIES")
    print("=" * 50)
    
    # Check if learning_data directory is empty (except for weight_configurations.json)
    learning_data_dir = Path("learning_data")
    if learning_data_dir.exists():
        remaining_files = list(learning_data_dir.iterdir())
        important_files = [f for f in remaining_files if f.name in ['weight_configurations.json', 'learning_metrics.json']]
        
        if len(remaining_files) == len(important_files):
            print(f"📁 learning_data/ contains only configuration files: {[f.name for f in important_files]}")
            print("✅ Keeping directory for configuration files")
        else:
            other_files = [f for f in remaining_files if f not in important_files]
            print(f"📁 learning_data/ contains other files: {[f.name for f in other_files]}")
    
    # Enhanced learning data should keep the JSON file
    enhanced_dir = Path("enhanced_learning_data")
    if enhanced_dir.exists():
        remaining_files = list(enhanced_dir.iterdir())
        print(f"📁 enhanced_learning_data/ contains: {[f.name for f in remaining_files]}")
        if any(f.name == 'contextual_predictions.json' for f in remaining_files):
            print("✅ Keeping directory for contextual predictions")


def verify_system_still_works():
    """Verify the systems still work after cleanup"""
    print("\n🧪 VERIFYING SYSTEMS STILL WORK")
    print("=" * 50)
    
    try:
        # Test enhanced learning system
        sys.path.append('.')
        from enhanced_adaptive_learning_system import enhanced_learning_system
        
        eligible_count = len(enhanced_learning_system.get_learning_eligible_predictions())
        print(f"✅ Enhanced learning system: {eligible_count} learning-eligible predictions")
        
        # Test main system
        from prediction_tracker import PredictionTracker
        tracker = PredictionTracker()
        ai_predictions = [p for p in tracker.predictions if getattr(p, 'is_ai_prediction', False)]
        print(f"✅ Main prediction system: {len(ai_predictions)} AI predictions")
        
        print("✅ All systems working correctly after cleanup")
        return True
        
    except Exception as e:
        print(f"❌ System verification failed: {e}")
        return False


def main():
    """Main cleanup process"""
    print("🧹 DATABASE CLEANUP PROCESS")
    print("=" * 60)
    print("This script will safely remove unnecessary database files")
    print("while preserving all important data and functionality.")
    print("=" * 60)
    
    # Step 1: Check for file locks
    locked_files = check_file_locks()
    
    if locked_files:
        print(f"\n⚠️ WARNING: Some files are locked by running processes:")
        for file in locked_files:
            print(f"   🔒 {file}")
        print(f"\n💡 SOLUTION:")
        print(f"   1. Close any running tennis calculator applications")
        print(f"   2. Close any database browsers or SQLite tools")
        print(f"   3. Run this script again")
        
        response = input(f"\nTry to proceed anyway? (y/N): ").lower().strip()
        if response != 'y':
            print("❌ Cleanup cancelled - close applications and try again")
            return
    
    # Step 2: Create final backup
    backup_dir, backed_up = create_final_backup()
    
    # Step 3: Show cleanup plan
    print(f"\n🗑️ CLEANUP PLAN:")
    print(f"   • Remove redundant enhanced_learning.db (data preserved in JSON)")
    print(f"   • Remove unused learning_database.db (completely empty)")
    print(f"   • Keep all JSON files and configuration files")
    
    response = input(f"\nProceed with cleanup? (y/N): ").lower().strip()
    if response != 'y':
        print("❌ Cleanup cancelled")
        return
    
    # Step 4: Perform cleanup
    print(f"\n🧹 PERFORMING CLEANUP")
    print("=" * 50)
    
    enhanced_removed = remove_redundant_enhanced_db()
    adaptive_removed = remove_unused_adaptive_db()
    
    # Step 5: Clean up directories
    cleanup_empty_directories()
    
    # Step 6: Verify systems still work
    systems_working = verify_system_still_works()
    
    # Step 7: Final report
    print(f"\n📋 CLEANUP SUMMARY")
    print("=" * 50)
    print(f"   Enhanced SQLite DB: {'✅ REMOVED' if enhanced_removed else '⚠️ KEPT'}")
    print(f"   Adaptive SQLite DB: {'✅ REMOVED' if adaptive_removed else '⚠️ KEPT'}")
    print(f"   Systems Working: {'✅ YES' if systems_working else '❌ NO'}")
    print(f"   Backup Location: {backup_dir}")
    
    if enhanced_removed and adaptive_removed and systems_working:
        print(f"\n🎉 CLEANUP COMPLETED SUCCESSFULLY!")
        print(f"   ✅ Removed unnecessary database files")
        print(f"   ✅ Preserved all important data")
        print(f"   ✅ All systems working correctly")
        print(f"\n📊 Final Storage Structure:")
        print(f"   📄 Main System: prediction_history.json")
        print(f"   🧠 Enhanced System: contextual_predictions.json")
        print(f"   🗑️ Redundant databases: REMOVED")
    else:
        print(f"\n⚠️ CLEANUP PARTIALLY COMPLETED")
        print(f"   Some files may still be locked or in use")
        print(f"   You can manually delete them when applications are closed")


if __name__ == "__main__":
    main()
