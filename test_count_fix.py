#!/usr/bin/env python3
"""
Test the deletion count fix
"""

def test_deletion_count_fix():
    """Test that the deletion count is now accurate"""
    print("🧪 TESTING DELETION COUNT FIX")
    print("=" * 50)
    
    try:
        from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem
        
        # Test 1: Check current state
        print("\n1️⃣ Current State:")
        current_system = EnhancedAdaptiveLearningSystem()
        current_eligible = current_system.get_learning_eligible_predictions()
        print(f"   Current learning-eligible count: {len(current_eligible)}")
        
        # Test 2: Simulate the new counting method (what the fix does)
        print("\n2️⃣ Testing Fresh Instance Method:")
        fresh_system = EnhancedAdaptiveLearningSystem()
        fresh_eligible = fresh_system.get_learning_eligible_predictions()
        print(f"   Fresh instance count: {len(fresh_eligible)}")
        
        # Test 3: Compare with old method (ComprehensiveSyncManager)
        print("\n3️⃣ Comparing with Old Method:")
        try:
            from comprehensive_sync_manager import ComprehensiveSyncManager
            sync_manager = ComprehensiveSyncManager()
            old_counts = sync_manager.force_refresh_learning_counts()
            print(f"   Old method count: {old_counts.get('enhanced_system_eligible', 'N/A')}")
        except Exception as e:
            print(f"   Old method error: {e}")
        
        # Test 4: Verify they should be the same
        print("\n4️⃣ Verification:")
        if len(current_eligible) == len(fresh_eligible):
            print("   ✅ Fresh instance method gives same result as current")
        else:
            print("   ❌ Fresh instance method gives different result")
            
        print(f"\n📊 Summary:")
        print(f"   Expected count after deleting 1 AI prediction: 1")
        print(f"   Current method would show: {len(fresh_eligible)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_deletion_count_fix()
