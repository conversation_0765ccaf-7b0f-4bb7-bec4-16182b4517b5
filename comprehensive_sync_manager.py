#!/usr/bin/env python3
"""
Comprehensive Synchronization Manager
Ensures all prediction systems stay synchronized and counts are accurate.
"""

import sys
import sqlite3
from datetime import datetime
from typing import Dict, Any, List, Tuple
from pathlib import Path

# Add current directory to path
sys.path.append('.')

try:
    from prediction_tracker import PredictionTracker
    from enhanced_adaptive_learning_system import enhanced_learning_system
    from learning_system_integration import learning_integrator
    from auto_completion_system import bulk_auto_complete_ai_predictions
    SYSTEMS_AVAILABLE = True
except ImportError as e:
    print(f"Error importing systems: {e}")
    SYSTEMS_AVAILABLE = False


class ComprehensiveSyncManager:
    """Manages synchronization between all prediction systems"""
    
    def __init__(self):
        self.main_tracker = PredictionTracker()
        self.enhanced_system = enhanced_learning_system
        self.learning_integrator = learning_integrator
        
    def get_all_system_counts(self) -> Dict[str, Any]:
        """Get counts from all systems for comparison"""
        
        # Main system (used by dashboard)
        main_predictions = self.main_tracker.predictions
        main_ai = [p for p in main_predictions if getattr(p, 'is_ai_prediction', False)]
        main_ai_with_outcomes = [p for p in main_ai if p.actual_winner]
        main_ai_completed = [p for p in main_ai_with_outcomes if getattr(p, 'match_status', None) == 'completed']
        
        # Enhanced system (used by optimization logs)
        enhanced_predictions = self.enhanced_system.contextual_predictions
        enhanced_with_outcomes = [p for p in enhanced_predictions if p.actual_winner]
        enhanced_ai = [p for p in enhanced_with_outcomes if getattr(p, 'is_ai_prediction', True)]
        enhanced_eligible = self.enhanced_system.get_learning_eligible_predictions()
        
        return {
            'main_system': {
                'total_predictions': len(main_predictions),
                'ai_predictions': len(main_ai),
                'ai_with_outcomes': len(main_ai_with_outcomes),
                'ai_completed': len(main_ai_completed)
            },
            'enhanced_system': {
                'total_predictions': len(enhanced_predictions),
                'with_outcomes': len(enhanced_with_outcomes),
                'ai_predictions': len(enhanced_ai),
                'learning_eligible': len(enhanced_eligible)
            },
            'timestamp': datetime.now().isoformat()
        }
    
    def detect_sync_issues(self) -> Dict[str, Any]:
        """Detect synchronization issues between systems"""
        counts = self.get_all_system_counts()
        
        issues = []
        
        # Check for major discrepancies
        main_ai_completed = counts['main_system']['ai_completed']
        enhanced_eligible = counts['enhanced_system']['learning_eligible']
        
        if abs(main_ai_completed - enhanced_eligible) > 5:  # Allow small differences
            issues.append({
                'type': 'count_mismatch',
                'description': f"Main system has {main_ai_completed} completed AI predictions, but enhanced system has {enhanced_eligible} eligible",
                'severity': 'high'
            })
        
        # Check for predictions with outcomes but wrong status
        enhanced_predictions = self.enhanced_system.contextual_predictions
        pending_with_outcomes = [p for p in enhanced_predictions 
                               if p.actual_winner and getattr(p, 'match_status', None) in ['pending', 'draft', None]]
        
        if pending_with_outcomes:
            issues.append({
                'type': 'status_mismatch',
                'description': f"{len(pending_with_outcomes)} predictions have outcomes but are not marked as completed",
                'severity': 'medium',
                'count': len(pending_with_outcomes)
            })
        
        return {
            'issues_found': len(issues),
            'issues': issues,
            'counts': counts,
            'sync_status': 'good' if len(issues) == 0 else 'needs_attention'
        }
    
    def auto_fix_sync_issues(self) -> Dict[str, Any]:
        """Automatically fix detected synchronization issues"""
        print("🔧 AUTO-FIXING SYNCHRONIZATION ISSUES")
        print("=" * 50)
        
        results = {
            'main_system_fixes': 0,
            'enhanced_system_fixes': 0,
            'database_fixes': 0,
            'total_fixes': 0
        }
        
        # Fix main system
        print("\n1️⃣ Fixing Main System...")
        main_result = bulk_auto_complete_ai_predictions(self.main_tracker.predictions)
        results['main_system_fixes'] = main_result['auto_completed']
        
        if main_result['auto_completed'] > 0:
            self.main_tracker.save_data()
            print(f"✅ Fixed {main_result['auto_completed']} predictions in main system")
        
        # Fix enhanced system
        print("\n2️⃣ Fixing Enhanced System...")
        enhanced_result = bulk_auto_complete_ai_predictions(self.enhanced_system.contextual_predictions)
        results['enhanced_system_fixes'] = enhanced_result['auto_completed']
        
        if enhanced_result['auto_completed'] > 0:
            self.enhanced_system.save_contextual_predictions()
            print(f"✅ Fixed {enhanced_result['auto_completed']} predictions in enhanced system")
        
        # Update database if needed
        print("\n3️⃣ Syncing Database...")
        db_fixes = self._sync_database_status()
        results['database_fixes'] = db_fixes
        
        results['total_fixes'] = results['main_system_fixes'] + results['enhanced_system_fixes'] + results['database_fixes']
        
        return results
    
    def _sync_database_status(self) -> int:
        """Sync database status with in-memory predictions"""
        fixes = 0
        
        try:
            db_path = self.enhanced_system.db_path
            if not Path(db_path).exists():
                return 0
            
            with sqlite3.connect(db_path) as conn:
                # Get predictions that should be completed but aren't in DB
                for pred in self.enhanced_system.contextual_predictions:
                    if (pred.actual_winner and 
                        getattr(pred, 'match_status', None) == 'completed' and
                        getattr(pred, 'is_ai_prediction', True)):
                        
                        # Update database record
                        cursor = conn.execute("""
                            UPDATE contextual_predictions 
                            SET actual_winner = ?, was_correct = ?
                            WHERE prediction_id = ?
                        """, (pred.actual_winner, getattr(pred, 'was_correct', None), pred.prediction_id))
                        
                        if cursor.rowcount > 0:
                            fixes += 1
                
                conn.commit()
                
        except Exception as e:
            print(f"Warning: Database sync error: {e}")
        
        if fixes > 0:
            print(f"✅ Synced {fixes} database records")
        
        return fixes
    
    def force_refresh_learning_counts(self) -> Dict[str, int]:
        """Force refresh of learning-eligible counts in all systems"""
        print("🔄 FORCE REFRESHING LEARNING COUNTS")
        print("=" * 50)
        
        # Clear any cached counts and recalculate
        enhanced_eligible = self.enhanced_system.get_learning_eligible_predictions()
        main_ai_completed = [p for p in self.main_tracker.predictions 
                           if (getattr(p, 'is_ai_prediction', False) and 
                               p.actual_winner and 
                               getattr(p, 'match_status', None) == 'completed')]
        
        counts = {
            'enhanced_system_eligible': len(enhanced_eligible),
            'main_system_completed': len(main_ai_completed)
        }
        
        print(f"📊 Refreshed Counts:")
        print(f"   Enhanced System Learning Eligible: {counts['enhanced_system_eligible']}")
        print(f"   Main System AI Completed: {counts['main_system_completed']}")
        
        return counts
    
    def comprehensive_sync_report(self) -> Dict[str, Any]:
        """Generate comprehensive synchronization report"""
        print("📋 COMPREHENSIVE SYNCHRONIZATION REPORT")
        print("=" * 60)
        
        # Get current counts
        counts = self.get_all_system_counts()
        
        # Detect issues
        sync_analysis = self.detect_sync_issues()
        
        # Check database consistency
        db_status = self._check_database_consistency()
        
        print(f"\n📊 SYSTEM COUNTS:")
        print(f"   Main System - Total: {counts['main_system']['total_predictions']}")
        print(f"   Main System - AI: {counts['main_system']['ai_predictions']}")
        print(f"   Main System - AI Completed: {counts['main_system']['ai_completed']}")
        print(f"   Enhanced System - Total: {counts['enhanced_system']['total_predictions']}")
        print(f"   Enhanced System - Learning Eligible: {counts['enhanced_system']['learning_eligible']}")
        
        print(f"\n🔍 SYNC STATUS: {sync_analysis['sync_status'].upper()}")
        if sync_analysis['issues']:
            print(f"   Issues Found: {len(sync_analysis['issues'])}")
            for issue in sync_analysis['issues']:
                print(f"   • {issue['description']}")
        else:
            print("   ✅ All systems synchronized")
        
        print(f"\n💾 DATABASE STATUS: {db_status['status']}")
        if db_status['issues']:
            print(f"   Database Issues: {len(db_status['issues'])}")
        
        return {
            'counts': counts,
            'sync_analysis': sync_analysis,
            'database_status': db_status,
            'timestamp': datetime.now().isoformat()
        }
    
    def _check_database_consistency(self) -> Dict[str, Any]:
        """Check database consistency with in-memory data"""
        try:
            db_path = self.enhanced_system.db_path
            if not Path(db_path).exists():
                return {'status': 'no_database', 'issues': []}
            
            with sqlite3.connect(db_path) as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM contextual_predictions WHERE actual_winner IS NOT NULL")
                db_count = cursor.fetchone()[0]
                
                memory_count = len([p for p in self.enhanced_system.contextual_predictions if p.actual_winner])
                
                issues = []
                if abs(db_count - memory_count) > 0:
                    issues.append(f"Database has {db_count} predictions with outcomes, memory has {memory_count}")
                
                return {
                    'status': 'consistent' if len(issues) == 0 else 'inconsistent',
                    'issues': issues,
                    'db_count': db_count,
                    'memory_count': memory_count
                }
                
        except Exception as e:
            return {'status': 'error', 'issues': [str(e)]}


def main():
    """Main function to run comprehensive sync management"""
    if not SYSTEMS_AVAILABLE:
        print("❌ Required systems not available. Please check imports.")
        return
    
    sync_manager = ComprehensiveSyncManager()
    
    print("🚀 COMPREHENSIVE SYNCHRONIZATION MANAGER")
    print("=" * 60)
    
    # Generate report
    report = sync_manager.comprehensive_sync_report()
    
    # Auto-fix if issues found
    if report['sync_analysis']['sync_status'] != 'good':
        print(f"\n🔧 ISSUES DETECTED - RUNNING AUTO-FIX")
        fix_results = sync_manager.auto_fix_sync_issues()
        
        print(f"\n📋 FIX SUMMARY:")
        print(f"   Total fixes applied: {fix_results['total_fixes']}")
        
        # Re-check after fixes
        print(f"\n🔄 RE-CHECKING AFTER FIXES...")
        updated_counts = sync_manager.force_refresh_learning_counts()
        
        print(f"\n✅ FINAL STATUS:")
        print(f"   Enhanced System Learning Eligible: {updated_counts['enhanced_system_eligible']}")
        print(f"   Main System AI Completed: {updated_counts['main_system_completed']}")
    else:
        print(f"\n✅ ALL SYSTEMS SYNCHRONIZED - NO FIXES NEEDED")


if __name__ == "__main__":
    main()
