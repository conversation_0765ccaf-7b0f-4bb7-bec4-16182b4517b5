#!/usr/bin/env python3
"""
Demo: Enhanced Prediction Deletion
Shows how the enhanced deletion system works across all AI learning systems
"""

from datetime import datetime
from prediction_tracker import PredictionTracker, PredictionRecord

def demo_enhanced_deletion():
    """Demonstrate the enhanced deletion functionality"""
    print("🎯 Enhanced Prediction Deletion Demo")
    print("=" * 50)
    
    # Create a sample prediction for demonstration
    print("\n1️⃣ Creating sample prediction...")
    tracker = PredictionTracker()
    
    # Add a test prediction
    test_prediction = PredictionRecord(
        timestamp=datetime.now().isoformat(),
        score=(3, 3),
        player1_name="Demo Player 1",
        player2_name="Demo Player 2",
        player1_code="DP1",
        player2_code="DP2",
        predicted_winner="DP1",
        prediction_probability=0.65,
        confidence=0.8,
        actual_winner="DP1",  # Set outcome for demonstration
        set_number=1,
        is_ai_prediction=True,
        session_id="demo_session",
        match_status="completed"
    )

    tracker.predictions.append(test_prediction)
    print(f"   Created prediction for demo")
    print(f"   Score: {test_prediction.score}")
    print(f"   Set: {test_prediction.set_number}")
    print(f"   Timestamp: {test_prediction.timestamp}")
    
    # Show what systems would be affected
    print("\n2️⃣ Systems that would be cleaned up:")
    
    # Check Enhanced Adaptive Learning System
    try:
        from enhanced_adaptive_learning_system import enhanced_learning_system
        contextual_count = len([p for p in enhanced_learning_system.contextual_predictions
                              if p.score == test_prediction.score and p.set_number == test_prediction.set_number])
        print(f"   • Enhanced Learning System: ~{contextual_count} potential matches")
    except Exception as e:
        print(f"   • Enhanced Learning System: Not available ({e})")
    
    # Check Enhanced Learning System V2
    try:
        from enhanced_adaptive_learning_v2 import enhanced_learning_system_v2
        v2_count = len([p for p in enhanced_learning_system_v2.prediction_tracker.predictions
                       if hasattr(p, 'score') and hasattr(p, 'set_number') and
                       p.score == test_prediction.score and p.set_number == test_prediction.set_number])
        print(f"   • Enhanced Learning V2: ~{v2_count} potential matches")
    except Exception as e:
        print(f"   • Enhanced Learning V2: Not available ({e})")
    
    # Check Adaptive Learning System Database
    try:
        import sqlite3
        from pathlib import Path
        db_path = Path("learning_data/learning_database.db")
        if db_path.exists():
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT COUNT(*) FROM weight_performance
                    WHERE set_number = ?
                """, (test_prediction.set_number,))
                db_count = cursor.fetchone()[0]
                print(f"   • Adaptive Learning DB: ~{db_count} potential matches")
        else:
            print("   • Adaptive Learning DB: Database not found")
    except Exception as e:
        print(f"   • Adaptive Learning DB: Error checking ({e})")
    
    # Demonstrate the deletion process (without actually deleting)
    print("\n3️⃣ Deletion process simulation:")
    print("   When you click 'Delete' in the GUI:")
    print("   1. Enhanced confirmation dialog appears")
    print("   2. User confirms deletion")
    print("   3. delete_prediction_from_ai_systems() is called")
    print("   4. Each AI system is cleaned up individually:")
    print("      - Enhanced Learning System (memory + database)")
    print("      - Enhanced Learning V2 (prediction tracker)")
    print("      - Adaptive Learning DB (weight_performance table)")
    print("      - EWMA Weights (accuracy history)")
    print("   5. Main prediction tracker is updated")
    print("   6. Detailed summary is shown to user")
    
    # Show the matching criteria
    print("\n4️⃣ Matching criteria used:")
    print(f"   • Score: {test_prediction.score} (exact match)")
    print(f"   • Set Number: {test_prediction.set_number} (exact match)")
    print(f"   • Timestamp: {test_prediction.timestamp[:19]} (±60 seconds tolerance)")
    
    # Show the benefits
    print("\n5️⃣ Benefits of enhanced deletion:")
    print("   ✅ Complete data integrity across all AI systems")
    print("   ✅ No residual incorrect data affecting future predictions")
    print("   ✅ Transparent process with detailed feedback")
    print("   ✅ Graceful error handling if systems unavailable")
    print("   ✅ Consistent matching logic across all systems")
    
    print("\n🎉 Demo complete! The enhanced deletion system is ready to use.")
    print("\nTo use in the GUI:")
    print("1. Go to Prediction Statistics tab")
    print("2. Click 'Delete' next to any prediction")
    print("3. Confirm in the enhanced dialog")
    print("4. Review the detailed deletion summary")

if __name__ == "__main__":
    demo_enhanced_deletion()
