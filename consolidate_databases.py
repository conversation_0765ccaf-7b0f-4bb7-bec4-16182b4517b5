#!/usr/bin/env python3
"""
Database Consolidation Script
Eliminates redundancy and unused databases while preserving necessary data.
"""

import sys
import sqlite3
import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# Add current directory to path
sys.path.append('.')


def backup_current_data():
    """Create backups before making changes"""
    print("💾 CREATING BACKUPS")
    print("=" * 50)
    
    backup_dir = Path(f"database_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    backup_dir.mkdir(exist_ok=True)
    
    files_to_backup = [
        "enhanced_learning_data/enhanced_learning.db",
        "enhanced_learning_data/contextual_predictions.json",
        "learning_data/learning_database.db",
        "prediction_history.json"
    ]
    
    backed_up = []
    for file_path in files_to_backup:
        source = Path(file_path)
        if source.exists():
            dest = backup_dir / source.name
            shutil.copy2(source, dest)
            backed_up.append(file_path)
            print(f"✅ Backed up: {file_path}")
        else:
            print(f"⚠️ Not found: {file_path}")
    
    print(f"\n📁 Backup created in: {backup_dir}")
    return backup_dir, backed_up


def analyze_enhanced_system_redundancy():
    """Analyze redundancy between JSON and SQLite in enhanced system"""
    print("\n🔍 ANALYZING ENHANCED SYSTEM REDUNDANCY")
    print("=" * 50)
    
    json_file = Path("enhanced_learning_data/contextual_predictions.json")
    db_file = Path("enhanced_learning_data/enhanced_learning.db")
    
    json_count = 0
    db_count = 0
    
    # Check JSON file
    if json_file.exists():
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
                json_count = len(data)
                print(f"📄 JSON file: {json_count} records")
        except Exception as e:
            print(f"❌ JSON error: {e}")
    
    # Check SQLite database
    if db_file.exists():
        try:
            with sqlite3.connect(db_file) as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM contextual_predictions")
                db_count = cursor.fetchone()[0]
                print(f"💾 SQLite DB: {db_count} records")
        except Exception as e:
            print(f"❌ SQLite error: {e}")
    
    # Analyze redundancy
    if json_count > 0 and db_count > 0:
        diff = abs(json_count - db_count)
        if diff <= 5:  # Allow small differences
            print(f"⚠️ REDUNDANCY DETECTED: JSON and SQLite have similar counts (diff: {diff})")
            return True
        else:
            print(f"✅ Different counts suggest different purposes (diff: {diff})")
            return False
    else:
        print(f"ℹ️ One or both storage systems empty")
        return False


def check_adaptive_database_usage():
    """Check if adaptive learning database is actually being used"""
    print("\n🔍 CHECKING ADAPTIVE DATABASE USAGE")
    print("=" * 50)
    
    db_file = Path("learning_data/learning_database.db")
    
    if not db_file.exists():
        print("📁 Adaptive database not found")
        return False
    
    try:
        with sqlite3.connect(db_file) as conn:
            cursor = conn.cursor()
            
            # Check all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            total_records = 0
            for table_name, in tables:
                if table_name != 'sqlite_sequence':  # Skip system table
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    print(f"📋 Table '{table_name}': {count} records")
                    total_records += count
            
            if total_records == 0:
                print("⚠️ UNUSED DATABASE: All tables are empty")
                return True  # True means it's safe to remove
            else:
                print(f"✅ Database in use: {total_records} total records")
                return False
                
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False


def remove_enhanced_sqlite_redundancy():
    """Remove SQLite redundancy from enhanced system"""
    print("\n🔧 REMOVING ENHANCED SYSTEM REDUNDANCY")
    print("=" * 50)
    
    db_file = Path("enhanced_learning_data/enhanced_learning.db")
    
    if not db_file.exists():
        print("📁 Enhanced SQLite database not found")
        return True
    
    try:
        # Check if we have data in JSON (primary storage)
        json_file = Path("enhanced_learning_data/contextual_predictions.json")
        if json_file.exists():
            with open(json_file, 'r') as f:
                json_data = json.load(f)
                if len(json_data) > 0:
                    print(f"✅ JSON file has {len(json_data)} records - safe to remove SQLite")
                    
                    # Remove the SQLite database
                    db_file.unlink()
                    print(f"🗑️ Removed: {db_file}")
                    return True
                else:
                    print("⚠️ JSON file is empty - keeping SQLite as backup")
                    return False
        else:
            print("⚠️ JSON file not found - keeping SQLite")
            return False
            
    except Exception as e:
        print(f"❌ Error removing SQLite redundancy: {e}")
        return False


def remove_unused_adaptive_database():
    """Remove unused adaptive learning database"""
    print("\n🔧 REMOVING UNUSED ADAPTIVE DATABASE")
    print("=" * 50)
    
    db_file = Path("learning_data/learning_database.db")
    
    if not db_file.exists():
        print("📁 Adaptive database not found")
        return True
    
    try:
        # Double-check it's really unused
        with sqlite3.connect(db_file) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM weight_performance")
            weight_count = cursor.fetchone()[0]
            cursor.execute("SELECT COUNT(*) FROM learning_sessions")
            session_count = cursor.fetchone()[0]
            
            if weight_count == 0 and session_count == 0:
                print(f"✅ Confirmed: Database is empty")
                db_file.unlink()
                print(f"🗑️ Removed: {db_file}")
                return True
            else:
                print(f"⚠️ Database has data: {weight_count} weights, {session_count} sessions")
                return False
                
    except Exception as e:
        print(f"❌ Error removing adaptive database: {e}")
        return False


def update_enhanced_system_code():
    """Update enhanced system to remove SQLite dependencies"""
    print("\n🔧 UPDATING ENHANCED SYSTEM CODE")
    print("=" * 50)
    
    # This would require modifying the enhanced_adaptive_learning_system.py file
    # For now, just provide instructions
    
    print("📝 Manual code updates needed:")
    print("1. In enhanced_adaptive_learning_system.py:")
    print("   - Remove: self._save_prediction_to_db(record)")
    print("   - Remove: database update in record_prediction_outcome()")
    print("   - Remove: init_enhanced_database() call")
    print("   - Keep: JSON-based storage methods")
    
    print("\n2. In comprehensive_sync_manager.py:")
    print("   - Remove: enhanced database sync logic")
    print("   - Remove: adaptive database references")
    
    print("\n3. Benefits after update:")
    print("   ✅ Faster prediction recording")
    print("   ✅ No JSON/SQLite sync issues")
    print("   ✅ Simpler maintenance")


def generate_consolidation_report():
    """Generate final consolidation report"""
    print("\n📋 CONSOLIDATION REPORT")
    print("=" * 50)
    
    # Check current state
    enhanced_json = Path("enhanced_learning_data/contextual_predictions.json")
    enhanced_db = Path("enhanced_learning_data/enhanced_learning.db")
    adaptive_db = Path("learning_data/learning_database.db")
    main_json = Path("prediction_history.json")
    
    print("📊 Final Storage Structure:")
    print(f"   Main System (prediction_history.json): {'✅ EXISTS' if main_json.exists() else '❌ MISSING'}")
    print(f"   Enhanced JSON (contextual_predictions.json): {'✅ EXISTS' if enhanced_json.exists() else '❌ MISSING'}")
    print(f"   Enhanced SQLite (enhanced_learning.db): {'❌ REMOVED' if not enhanced_db.exists() else '⚠️ STILL EXISTS'}")
    print(f"   Adaptive SQLite (learning_database.db): {'❌ REMOVED' if not adaptive_db.exists() else '⚠️ STILL EXISTS'}")
    
    print("\n🎯 System Purposes:")
    print("   📄 Main System: Complete prediction tracking (all types)")
    print("   🧠 Enhanced System: AI learning with contextual factors")
    
    print("\n✅ Benefits Achieved:")
    if not enhanced_db.exists():
        print("   ✅ Eliminated Enhanced System redundancy")
    if not adaptive_db.exists():
        print("   ✅ Removed unused Adaptive database")
    
    print("\n📝 Next Steps:")
    print("   1. Update code to remove SQLite dependencies")
    print("   2. Test prediction recording and learning")
    print("   3. Verify sync manager works with new structure")


def main():
    """Main consolidation process"""
    print("🚀 DATABASE CONSOLIDATION PROCESS")
    print("=" * 60)
    print("This script will eliminate redundant databases while preserving")
    print("necessary data and maintaining system functionality.")
    print("=" * 60)
    
    # Step 1: Create backups
    backup_dir, backed_up = backup_current_data()
    
    # Step 2: Analyze current state
    has_redundancy = analyze_enhanced_system_redundancy()
    adaptive_unused = check_adaptive_database_usage()
    
    # Step 3: Ask for confirmation
    print(f"\n🤔 CONSOLIDATION PLAN:")
    if has_redundancy:
        print("   🔧 Remove Enhanced System SQLite redundancy")
    if adaptive_unused:
        print("   🗑️ Remove unused Adaptive Learning database")
    
    if not has_redundancy and not adaptive_unused:
        print("   ✅ No consolidation needed - systems are already optimal")
        return
    
    response = input(f"\nProceed with consolidation? (y/N): ").lower().strip()
    if response != 'y':
        print("❌ Consolidation cancelled")
        return
    
    # Step 4: Perform consolidation
    success = True
    
    if has_redundancy:
        if not remove_enhanced_sqlite_redundancy():
            success = False
    
    if adaptive_unused:
        if not remove_unused_adaptive_database():
            success = False
    
    # Step 5: Update code (manual step)
    if success:
        update_enhanced_system_code()
    
    # Step 6: Generate report
    generate_consolidation_report()
    
    if success:
        print(f"\n🎉 CONSOLIDATION COMPLETED SUCCESSFULLY!")
        print(f"📁 Backups available in: {backup_dir}")
    else:
        print(f"\n⚠️ CONSOLIDATION PARTIALLY COMPLETED")
        print(f"📁 Backups available in: {backup_dir}")


if __name__ == "__main__":
    main()
