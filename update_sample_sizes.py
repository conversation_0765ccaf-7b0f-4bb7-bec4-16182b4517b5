#!/usr/bin/env python3
"""
Update Sample Sizes Script
Updates both learning systems with research-based optimal sample sizes.
"""

import json
from pathlib import Path
from adaptive_learning_system import AdaptiveLearningSystem, EnhancedSampleRequirements
from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem

def update_base_system_sample_sizes():
    """Update base adaptive learning system with research-based sample sizes"""
    print("🔄 Updating Base Adaptive Learning System...")
    
    # Create system instance
    system = AdaptiveLearningSystem()
    
    # Force update sample requirements to new research-based values
    system.sample_requirements = EnhancedSampleRequirements()
    
    # Verify the new values
    print(f"✅ Min sample size updated: {system.min_sample_size}")
    print(f"✅ Weight type minimums updated: {dict(system.sample_requirements.weight_type_minimums)}")
    print(f"✅ Context minimums updated: {dict(system.sample_requirements.context_minimums)}")
    print(f"✅ Surface multipliers updated: {dict(system.sample_requirements.surface_multipliers)}")
    
    # Force save the new configuration
    system.save_configurations()
    print("✅ Base system configuration saved!")
    
    return system

def update_enhanced_system_sample_sizes():
    """Update enhanced adaptive learning system with research-based sample sizes"""
    print("\n🔄 Updating Enhanced Adaptive Learning System...")
    
    # Create system instance
    system = EnhancedAdaptiveLearningSystem()
    
    # Verify the new values
    print(f"✅ Min sample size updated: {system.min_sample_size}")
    print(f"✅ Learning rate: {system.learning_rate}")
    print(f"✅ Max balance change: {system.max_balance_change}")
    
    # Force save the new configuration
    system.save_balance_configuration()
    print("✅ Enhanced system configuration saved!")
    
    return system

def verify_updates():
    """Verify that the updates were applied correctly"""
    print("\n🔍 Verifying updates...")
    
    # Test base system
    base_system = AdaptiveLearningSystem()
    print(f"Base system min_sample_size: {base_system.min_sample_size}")
    
    # Test enhanced system  
    enhanced_system = EnhancedAdaptiveLearningSystem()
    print(f"Enhanced system min_sample_size: {enhanced_system.min_sample_size}")
    
    # Check if weight type minimums are updated
    momentum_intensity_min = base_system.sample_requirements.weight_type_minimums.get('momentum_intensity_weight', 0)
    print(f"Momentum intensity minimum: {momentum_intensity_min} (should be 140)")
    
    # Check if context minimums are updated
    early_set_min = base_system.sample_requirements.context_minimums.get('early_set', 0)
    print(f"Early set minimum: {early_set_min} (should be 70)")
    
    if momentum_intensity_min == 140 and early_set_min == 70:
        print("✅ All updates verified successfully!")
        return True
    else:
        print("❌ Updates not fully applied - may need manual cache clearing")
        return False

def clear_cached_configs():
    """Clear cached configuration files to force reload of new defaults"""
    print("\n🧹 Clearing cached configuration files...")
    
    config_files = [
        "learning_data/weight_configurations.json",
        "enhanced_learning_data/balance_configuration.json",
        "enhanced_learning_data/contextual_predictions.json"
    ]
    
    for config_file in config_files:
        config_path = Path(config_file)
        if config_path.exists():
            try:
                config_path.unlink()
                print(f"✅ Removed {config_file}")
            except Exception as e:
                print(f"⚠️ Could not remove {config_file}: {e}")
        else:
            print(f"ℹ️ {config_file} does not exist")

def main():
    """Main update process"""
    print("🚀 Starting Sample Size Update Process")
    print("=" * 50)
    
    # Clear cached configs first
    clear_cached_configs()
    
    # Update both systems
    base_system = update_base_system_sample_sizes()
    enhanced_system = update_enhanced_system_sample_sizes()
    
    # Verify updates
    success = verify_updates()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Sample size update completed successfully!")
        print("\nNew Research-Based Settings Applied:")
        print("- Base system minimum: 100 samples (up from 20)")
        print("- Enhanced system minimum: 200 samples (up from 40)")
        print("- Weight-type minimums: 80-150 samples (up from 20-50)")
        print("- Context minimums: 70-160 samples (up from 15-35)")
    else:
        print("⚠️ Sample size update completed with warnings")
        print("You may need to restart the application to see all changes")

if __name__ == "__main__":
    main()
