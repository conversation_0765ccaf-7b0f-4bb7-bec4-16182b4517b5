#!/usr/bin/env python3
"""
Test script for the improved break point detection logic in tennis.py
Tests complex scenarios with multiple ADs and BPs that previously caused issues.
"""

def test_complex_bp_ad_scenarios():
    """Test complex scenarios that previously failed"""
    
    # Test case 1: Multiple ADs with BP - should detect complexity and use heuristic
    test_case_1 = {
        'tennis_scores': ['AD', '40', 'AD', '40', 'AD', '40', 'AD'],
        'description': 'Multiple AD-40 sequences ending with AD',
        'expected_complexity': True
    }
    
    # Test case 2: Very long sequence - should be marked as complex
    test_case_2 = {
        'tennis_scores': ['AD', '40', 'AD', '40', 'AD', '40', 'AD', '40', 'AD', '40'],
        'description': 'Very long sequence (10 scores)',
        'expected_complexity': True
    }
    
    # Test case 3: Simple BP -> AD -> 40 - should handle correctly
    test_case_3 = {
        'tennis_scores': ['AD', '40'],
        'description': 'Simple AD followed by 40',
        'expected_complexity': False
    }
    
    # Test case 4: BP -> AD (immediate) - should detect break
    test_case_4 = {
        'tennis_scores': ['AD'],
        'description': 'Single AD after BP',
        'expected_complexity': False
    }
    
    test_cases = [test_case_1, test_case_2, test_case_3, test_case_4]
    
    print("🎾 Testing Improved Break Point Detection Logic")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest Case {i}: {test_case['description']}")
        print(f"Tennis scores: {test_case['tennis_scores']}")
        
        # Simulate the complexity detection logic
        tennis_scores = test_case['tennis_scores']
        ad_count = tennis_scores.count('AD')
        total_scores = len(tennis_scores)
        
        is_complex = ad_count >= 3 or total_scores >= 8
        
        print(f"AD count: {ad_count}, Total scores: {total_scores}")
        print(f"Detected as complex: {is_complex}")
        print(f"Expected complexity: {test_case['expected_complexity']}")
        
        if is_complex == test_case['expected_complexity']:
            print("✅ PASS - Complexity detection correct")
        else:
            print("❌ FAIL - Complexity detection incorrect")
        
        # Test the simplified heuristic for complex cases
        if is_complex:
            if total_scores >= 10:
                result = "Server likely held (very long sequence)"
            elif tennis_scores[-1] == 'AD':
                result = "Using position logic for final AD"
            else:
                result = "Assuming break occurred (conservative)"
            
            print(f"Heuristic result: {result}")
        
        print("-" * 40)

def test_break_point_theory_scenarios():
    """Test the main Break Point Theory logic"""
    
    print("\n🎾 Testing Break Point Theory Scenarios")
    print("=" * 60)
    
    scenarios = [
        {
            'description': 'Simple hold - no BP',
            'has_bp': False,
            'winner': 'ALB',
            'expected_server': 'ALB',
            'logic': 'No BP: Winner held serve'
        },
        {
            'description': 'Simple break - BP present',
            'has_bp': True,
            'winner': 'ALB',
            'expected_server': 'ZVE',  # Other player started serving
            'logic': 'BP present: Winner broke serve'
        },
        {
            'description': 'Complex game with issues',
            'has_bp': True,
            'winner': 'ZVE',
            'expected_server': 'ALB',
            'logic': 'BP present: Winner broke serve',
            'has_issues': True
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\nScenario {i}: {scenario['description']}")
        print(f"Has BP: {scenario['has_bp']}")
        print(f"Winner: {scenario['winner']}")
        
        # Apply Break Point Theory
        if scenario['has_bp']:
            # BP present: Winner broke serve → Other player started serving
            if scenario['winner'] == 'ALB':
                starting_server = 'ZVE'
            else:
                starting_server = 'ALB'
            logic = f"BP detected. {scenario['winner']} broke serve → {starting_server} started serving"
        else:
            # No BP: Winner held serve → That player started serving
            starting_server = scenario['winner']
            logic = f"No BP. {scenario['winner']} held serve → {starting_server} started serving"
        
        print(f"Detected starting server: {starting_server}")
        print(f"Expected starting server: {scenario['expected_server']}")
        print(f"Logic: {logic}")
        
        if starting_server == scenario['expected_server']:
            print("✅ PASS - Starting server detection correct")
        else:
            print("❌ FAIL - Starting server detection incorrect")
        
        if scenario.get('has_issues'):
            print("⚠️  Complex scenario - marked as having issues")
        
        print("-" * 40)

def test_position_based_ad_logic():
    """Test the position-based AD analysis"""
    
    print("\n🎾 Testing Position-Based AD Logic")
    print("=" * 60)
    
    # Test different AD positions
    test_positions = [
        {'ad_position': 0, 'description': 'AD at position 0 (Player 1)'},
        {'ad_position': 1, 'description': 'AD at position 1 (Player 2)'},
        {'ad_position': 2, 'description': 'AD at position 2 (Player 1)'},
        {'ad_position': 3, 'description': 'AD at position 3 (Player 2)'},
        {'ad_position': 5, 'description': 'AD at position 5 (Player 2)'},
    ]
    
    for test in test_positions:
        ad_position = test['ad_position']
        player_with_ad = 'Player1' if ad_position % 2 == 0 else 'Player2'
        
        print(f"\n{test['description']}")
        print(f"Position {ad_position} → {player_with_ad} had advantage")
        
        # Simple heuristic: short sequence = break, longer = hold
        if ad_position <= 3:
            result = "Break likely occurred (short sequence)"
        else:
            result = "Hold likely occurred (longer sequence)"
        
        print(f"Heuristic result: {result}")
        print("-" * 40)

if __name__ == "__main__":
    print("🎾 IMPROVED BREAK POINT DETECTION TEST SUITE")
    print("=" * 60)
    print("Testing the enhanced logic for handling complex BP/AD scenarios")
    print("This addresses issues where auto-detection failed with too many ADs and BPs")
    
    test_complex_bp_ad_scenarios()
    test_break_point_theory_scenarios()
    test_position_based_ad_logic()
    
    print("\n" + "=" * 60)
    print("✅ Test suite completed!")
    print("The improved logic should now handle complex scenarios better")
    print("and mark uncertain cases as having issues for user review.")
