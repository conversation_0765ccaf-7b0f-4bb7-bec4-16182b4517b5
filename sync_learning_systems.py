#!/usr/bin/env python3
"""
Sync Learning Systems Data

This script synchronizes match statuses between the regular prediction system
and the enhanced learning system to ensure data consistency.
"""

import json
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
import sys

# Add the current directory to Python path for imports
sys.path.append('.')

try:
    from prediction_tracker import PredictionTracker
    from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem, ContextualPredictionRecord
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Make sure you're running this from the tennis calculator directory")
    sys.exit(1)


class LearningSystemSynchronizer:
    """Synchronizes data between regular and enhanced learning systems"""
    
    def __init__(self):
        self.prediction_tracker = PredictionTracker()
        self.enhanced_system = EnhancedAdaptiveLearningSystem()
        
    def analyze_current_state(self) -> Dict:
        """Analyze the current state of both systems"""
        print("🔍 Analyzing current state of learning systems...")
        
        # Regular system analysis
        regular_predictions = self.prediction_tracker.predictions
        regular_with_outcomes = [p for p in regular_predictions if p.actual_winner]
        regular_completed = [p for p in regular_predictions 
                           if p.actual_winner and getattr(p, 'match_status', None) == 'completed']
        
        # Enhanced system analysis (JSON)
        enhanced_predictions = self.enhanced_system.contextual_predictions
        enhanced_with_outcomes = [p for p in enhanced_predictions if p.actual_winner]
        enhanced_completed = [p for p in enhanced_predictions 
                            if p.actual_winner and getattr(p, 'match_status', None) == 'completed']
        enhanced_eligible = self.enhanced_system.get_learning_eligible_predictions()
        
        # Database analysis
        db_total, db_completed = self._get_database_stats()
        
        analysis = {
            'regular_system': {
                'total_predictions': len(regular_predictions),
                'with_outcomes': len(regular_with_outcomes),
                'completed_status': len(regular_completed)
            },
            'enhanced_system_json': {
                'total_predictions': len(enhanced_predictions),
                'with_outcomes': len(enhanced_with_outcomes),
                'completed_status': len(enhanced_completed),
                'learning_eligible': len(enhanced_eligible)
            },
            'enhanced_system_db': {
                'total_predictions': db_total,
                'with_outcomes': db_completed
            }
        }
        
        return analysis
    
    def _get_database_stats(self) -> tuple:
        """Get statistics from the enhanced system database"""
        try:
            with sqlite3.connect(self.enhanced_system.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM contextual_predictions')
                total = cursor.fetchone()[0]
                cursor.execute('SELECT COUNT(*) FROM contextual_predictions WHERE actual_winner IS NOT NULL')
                completed = cursor.fetchone()[0]
                return total, completed
        except Exception as e:
            print(f"Error reading database: {e}")
            return 0, 0
    
    def sync_match_statuses(self, dry_run: bool = True) -> Dict:
        """Sync match statuses from regular system to enhanced system"""
        print(f"🔄 {'DRY RUN: ' if dry_run else ''}Syncing match statuses...")
        
        # Create a mapping of session_id -> match_status from regular system
        session_status_map = {}
        for pred in self.prediction_tracker.predictions:
            if hasattr(pred, 'session_id') and pred.session_id and hasattr(pred, 'match_status'):
                session_status_map[pred.session_id] = pred.match_status
        
        # Also create a mapping based on prediction similarity (timestamp, players)
        prediction_status_map = {}
        for pred in self.prediction_tracker.predictions:
            if pred.actual_winner and hasattr(pred, 'match_status'):
                key = (pred.timestamp[:16], pred.player1_name, pred.player2_name)  # Match by timestamp (to minute), players
                prediction_status_map[key] = pred.match_status
        
        updates_made = 0
        updates_by_session = 0
        updates_by_similarity = 0
        
        for enhanced_pred in self.enhanced_system.contextual_predictions:
            old_status = getattr(enhanced_pred, 'match_status', 'unknown')
            new_status = None
            update_method = None
            
            # Try to match by session_id first
            if hasattr(enhanced_pred, 'session_id') and enhanced_pred.session_id:
                if enhanced_pred.session_id in session_status_map:
                    new_status = session_status_map[enhanced_pred.session_id]
                    update_method = 'session_id'
                    updates_by_session += 1
            
            # If no session match and prediction has actual_winner, try similarity matching
            if not new_status and enhanced_pred.actual_winner:
                # Try to find a matching prediction in regular system
                enhanced_key = (enhanced_pred.timestamp[:16], 
                              enhanced_pred.predicted_winner,  # Use predicted_winner as proxy
                              enhanced_pred.actual_winner)
                
                # Look for similar predictions
                for regular_pred in self.prediction_tracker.predictions:
                    if (regular_pred.actual_winner == enhanced_pred.actual_winner and
                        regular_pred.timestamp[:16] == enhanced_pred.timestamp[:16] and
                        hasattr(regular_pred, 'match_status')):
                        new_status = regular_pred.match_status
                        update_method = 'similarity'
                        updates_by_similarity += 1
                        break
            
            # Apply the update
            if new_status and new_status != old_status:
                if not dry_run:
                    enhanced_pred.match_status = new_status
                
                updates_made += 1
                print(f"  {'[DRY RUN] ' if dry_run else ''}Updated {enhanced_pred.prediction_id}: "
                      f"{old_status} -> {new_status} (via {update_method})")
        
        # Save changes if not dry run
        if not dry_run and updates_made > 0:
            self.enhanced_system.save_contextual_predictions()
            print(f"✅ Saved {updates_made} updates to enhanced system JSON")
        
        return {
            'total_updates': updates_made,
            'updates_by_session': updates_by_session,
            'updates_by_similarity': updates_by_similarity,
            'dry_run': dry_run
        }
    
    def mark_completed_predictions_as_completed(self, dry_run: bool = True) -> Dict:
        """Mark all predictions with actual_winner as completed if they don't have a status"""
        print(f"🔄 {'DRY RUN: ' if dry_run else ''}Marking predictions with outcomes as completed...")
        
        updates_made = 0
        
        for enhanced_pred in self.enhanced_system.contextual_predictions:
            if (enhanced_pred.actual_winner and 
                (not hasattr(enhanced_pred, 'match_status') or 
                 enhanced_pred.match_status in ['pending', 'draft', None])):
                
                old_status = getattr(enhanced_pred, 'match_status', 'unknown')
                
                if not dry_run:
                    enhanced_pred.match_status = 'completed'
                
                updates_made += 1
                print(f"  {'[DRY RUN] ' if dry_run else ''}Updated {enhanced_pred.prediction_id}: "
                      f"{old_status} -> completed")
        
        # Save changes if not dry run
        if not dry_run and updates_made > 0:
            self.enhanced_system.save_contextual_predictions()
            print(f"✅ Saved {updates_made} updates to enhanced system JSON")
        
        return {
            'total_updates': updates_made,
            'dry_run': dry_run
        }


def main():
    """Main synchronization function"""
    print("🔧 Learning Systems Synchronization Tool")
    print("=" * 50)
    
    synchronizer = LearningSystemSynchronizer()
    
    # Step 1: Analyze current state
    print("\n📊 STEP 1: Current State Analysis")
    analysis = synchronizer.analyze_current_state()
    
    print("\nRegular Prediction System:")
    print(f"  Total predictions: {analysis['regular_system']['total_predictions']}")
    print(f"  With outcomes: {analysis['regular_system']['with_outcomes']}")
    print(f"  Completed status: {analysis['regular_system']['completed_status']}")
    
    print("\nEnhanced System (JSON):")
    print(f"  Total predictions: {analysis['enhanced_system_json']['total_predictions']}")
    print(f"  With outcomes: {analysis['enhanced_system_json']['with_outcomes']}")
    print(f"  Completed status: {analysis['enhanced_system_json']['completed_status']}")
    print(f"  Learning eligible: {analysis['enhanced_system_json']['learning_eligible']}")
    
    print("\nEnhanced System (Database):")
    print(f"  Total predictions: {analysis['enhanced_system_db']['total_predictions']}")
    print(f"  With outcomes: {analysis['enhanced_system_db']['with_outcomes']}")
    
    # Step 2: Dry run sync
    print("\n🧪 STEP 2: Dry Run Synchronization")
    sync_result = synchronizer.sync_match_statuses(dry_run=True)
    print(f"\nDry run results:")
    print(f"  Total updates needed: {sync_result['total_updates']}")
    print(f"  Updates by session ID: {sync_result['updates_by_session']}")
    print(f"  Updates by similarity: {sync_result['updates_by_similarity']}")
    
    # Step 3: Fallback dry run
    print("\n🧪 STEP 3: Fallback Strategy (Mark completed)")
    fallback_result = synchronizer.mark_completed_predictions_as_completed(dry_run=True)
    print(f"\nFallback dry run results:")
    print(f"  Additional updates needed: {fallback_result['total_updates']}")

    # Step 4: Ask for confirmation
    print("\n❓ STEP 4: Confirmation")
    total_changes = sync_result['total_updates'] + fallback_result['total_updates']

    if total_changes == 0:
        print("✅ No synchronization needed - systems are already in sync!")
        return

    print(f"Ready to make {total_changes} total changes to fix the synchronization.")
    response = input("Do you want to proceed with the actual sync? (y/N): ").strip().lower()

    if response == 'y':
        print("\n🚀 STEP 5: Executing Synchronization")

        # Execute actual sync
        sync_result = synchronizer.sync_match_statuses(dry_run=False)
        fallback_result = synchronizer.mark_completed_predictions_as_completed(dry_run=False)

        print(f"\n✅ Synchronization completed!")
        print(f"  Session-based updates: {sync_result['updates_by_session']}")
        print(f"  Similarity-based updates: {sync_result['updates_by_similarity']}")
        print(f"  Fallback updates: {fallback_result['total_updates']}")

        # Final verification
        print("\n🔍 STEP 6: Verification")
        final_analysis = synchronizer.analyze_current_state()
        final_eligible = final_analysis['enhanced_system_json']['learning_eligible']
        print(f"Learning eligible predictions after sync: {final_eligible}")

        if final_eligible > 0:
            print("🎉 Success! Enhanced learning system can now optimize with the synced data.")
        else:
            print("⚠️  Still no learning eligible predictions. Manual investigation may be needed.")

    else:
        print("❌ Synchronization cancelled.")


if __name__ == "__main__":
    main()
