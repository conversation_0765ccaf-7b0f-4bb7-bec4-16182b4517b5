#!/usr/bin/env python3
"""
Debug the current deletion issue with the new prediction
"""

def debug_current_deletion():
    """Debug why deletion is still failing"""
    print("🔍 DEBUGGING CURRENT DELETION ISSUE")
    print("=" * 50)
    
    try:
        from enhanced_adaptive_learning_system import enhanced_learning_system
        from prediction_tracker import PredictionTracker
        from datetime import datetime
        
        # Check current state
        print("\n1️⃣ Current State:")
        enhanced_predictions = enhanced_learning_system.contextual_predictions
        print(f"   Enhanced system predictions: {len(enhanced_predictions)}")
        
        main_tracker = PredictionTracker()
        main_predictions = main_tracker.predictions
        print(f"   Main tracker predictions: {len(main_predictions)}")
        
        # Show details of both systems
        print("\n2️⃣ Enhanced System Predictions:")
        for i, pred in enumerate(enhanced_predictions):
            print(f"   Prediction {i+1}:")
            print(f"     Score: {pred.score} (type: {type(pred.score)})")
            print(f"     Set: {pred.set_number}")
            print(f"     Timestamp: {pred.timestamp}")
            print(f"     Predicted: {pred.predicted_winner}")
            print(f"     Actual: {pred.actual_winner}")
            print()
        
        print("3️⃣ Main Tracker Predictions:")
        for i, pred in enumerate(main_predictions):
            print(f"   Prediction {i+1}:")
            print(f"     Score: {pred.score} (type: {type(pred.score)})")
            print(f"     Set: {pred.set_number}")
            print(f"     Timestamp: {pred.timestamp}")
            print(f"     Predicted: {pred.predicted_winner}")
            print(f"     Actual: {getattr(pred, 'actual_winner', 'None')}")
            print()
        
        # Test deletion matching with actual data
        if enhanced_predictions and main_predictions:
            print("4️⃣ Testing Deletion Matching:")
            
            main_pred = main_predictions[0]
            enhanced_pred = enhanced_predictions[0]
            
            print(f"   Main prediction to delete:")
            print(f"     Score: {main_pred.score} (type: {type(main_pred.score)})")
            print(f"     Set: {main_pred.set_number}")
            print(f"     Timestamp: {main_pred.timestamp}")
            
            print(f"   Enhanced prediction to match:")
            print(f"     Score: {enhanced_pred.score} (type: {type(enhanced_pred.score)})")
            print(f"     Set: {enhanced_pred.set_number}")
            print(f"     Timestamp: {enhanced_pred.timestamp}")
            
            # Test the matching logic
            target_timestamp = datetime.fromisoformat(main_pred.timestamp.replace('Z', '+00:00'))
            enhanced_timestamp = datetime.fromisoformat(enhanced_pred.timestamp.replace('Z', '+00:00'))
            time_diff = abs((enhanced_timestamp - target_timestamp).total_seconds())
            
            # Test score matching with our fix
            pred_score = tuple(enhanced_pred.score) if isinstance(enhanced_pred.score, list) else enhanced_pred.score
            target_score = tuple(main_pred.score) if isinstance(main_pred.score, list) else main_pred.score
            
            print(f"   Matching Analysis:")
            print(f"     Enhanced score (normalized): {pred_score}")
            print(f"     Main score (normalized): {target_score}")
            print(f"     Scores match: {pred_score == target_score}")
            print(f"     Set numbers match: {enhanced_pred.set_number == main_pred.set_number}")
            print(f"     Time difference: {time_diff} seconds")
            print(f"     Time within tolerance (60s): {time_diff <= 60}")
            
            overall_match = (
                pred_score == target_score and
                enhanced_pred.set_number == main_pred.set_number and
                time_diff <= 60
            )
            print(f"     Overall match: {overall_match}")
            
            # Test actual deletion
            if overall_match:
                print(f"\n5️⃣ Testing Actual Deletion:")
                initial_count = len(enhanced_learning_system.contextual_predictions)
                
                deleted_count = enhanced_learning_system.delete_prediction_by_criteria(
                    main_pred.score, main_pred.set_number, main_pred.timestamp
                )
                
                final_count = len(enhanced_learning_system.contextual_predictions)
                
                print(f"     Initial count: {initial_count}")
                print(f"     Deleted count: {deleted_count}")
                print(f"     Final count: {final_count}")
                
                if deleted_count > 0:
                    print("     ✅ Deletion successful!")
                    # Restore for next test
                    enhanced_learning_system.load_contextual_predictions()
                else:
                    print("     ❌ Deletion failed!")
            else:
                print(f"\n5️⃣ Deletion would fail due to matching issues")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_current_deletion()
