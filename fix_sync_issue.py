#!/usr/bin/env python3
"""
Fix the sync issue between main prediction system and enhanced learning system.
This will make the enhanced learning system show the correct number of learning-eligible predictions.
"""

def fix_prediction_sync():
    """Fix the sync between prediction systems"""
    
    try:
        from enhanced_adaptive_learning_system import enhanced_learning_system
        
        print("🔧 FIXING PREDICTION SYNC ISSUE")
        print("=" * 50)
        
        # Get all contextual predictions with outcomes but wrong status
        predictions_to_fix = []
        for pred in enhanced_learning_system.contextual_predictions:
            if (pred.actual_winner and 
                getattr(pred, 'match_status', None) in ['pending', 'draft', None]):
                predictions_to_fix.append(pred)
        
        print(f"📊 Found {len(predictions_to_fix)} predictions to fix")
        
        if len(predictions_to_fix) > 0:
            # Update their status to completed
            for pred in predictions_to_fix:
                pred.match_status = 'completed'
                print(f"   ✅ Fixed: {pred.prediction_id} - {pred.predicted_winner} vs {pred.actual_winner}")
            
            # Save the changes
            enhanced_learning_system.save_contextual_predictions()
            print(f"\n💾 Saved changes to enhanced learning system")
            
            # Verify the fix
            learning_eligible = enhanced_learning_system.get_learning_eligible_predictions()
            print(f"🎯 Learning-eligible predictions after fix: {len(learning_eligible)}")
            
            print(f"\n✅ SYNC ISSUE FIXED!")
            print(f"   The enhanced learning system should now show the correct number")
            print(f"   of learning-eligible predictions in future optimization logs.")
            
        else:
            print("✅ No predictions need fixing - systems are already in sync!")
            
    except Exception as e:
        print(f"❌ Error fixing sync: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_prediction_sync()
