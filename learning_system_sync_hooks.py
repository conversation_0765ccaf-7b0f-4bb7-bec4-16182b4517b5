#!/usr/bin/env python3
"""
Learning System Synchronization Hooks

This module provides automatic synchronization hooks to keep the regular
and enhanced learning systems in sync for future predictions.
"""

from typing import Dict, Any, Optional
import sys
from datetime import datetime

# Add the current directory to Python path for imports
sys.path.append('.')

try:
    from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem
    from prediction_tracker import PredictionTracker
    from auto_completion_system import auto_complete_on_outcome
    AUTO_COMPLETION_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import learning systems: {e}")
    AUTO_COMPLETION_AVAILABLE = False


class LearningSystemSyncHooks:
    """Provides hooks to keep learning systems synchronized"""
    
    def __init__(self):
        self.enhanced_system = None
        self.prediction_tracker = None
        self._initialize_systems()
    
    def _initialize_systems(self):
        """Initialize the learning systems"""
        try:
            self.enhanced_system = EnhancedAdaptiveLearningSystem()
            self.prediction_tracker = PredictionTracker()
        except Exception as e:
            print(f"Warning: Could not initialize learning systems: {e}")
    
    def sync_match_status_update(self, session_id: str, new_status: str) -> Dict[str, Any]:
        """
        Hook to sync match status updates between systems
        Call this whenever a match status is updated in the regular system
        """
        if not self.enhanced_system or not session_id:
            return {'synced': False, 'reason': 'System not available or no session_id'}
        
        try:
            # Update enhanced system
            updated_count = self.enhanced_system.update_match_status(session_id, new_status)
            
            return {
                'synced': True,
                'session_id': session_id,
                'new_status': new_status,
                'enhanced_predictions_updated': updated_count,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'synced': False,
                'session_id': session_id,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def sync_prediction_outcome(self, prediction_id: str, actual_winner: str, 
                              session_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Hook to sync prediction outcomes between systems
        Call this whenever a prediction outcome is recorded
        """
        if not self.enhanced_system:
            return {'synced': False, 'reason': 'Enhanced system not available'}
        
        try:
            # Record outcome in enhanced system
            self.enhanced_system.record_prediction_outcome(prediction_id, actual_winner)
            
            # If we have a session_id and the match is completed, update status
            sync_result = None
            if session_id:
                # Check if this was the last prediction for the session
                # If so, mark the session as completed
                sync_result = self.sync_match_status_update(session_id, 'completed')
            
            return {
                'synced': True,
                'prediction_id': prediction_id,
                'actual_winner': actual_winner,
                'session_sync': sync_result,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'synced': False,
                'prediction_id': prediction_id,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def auto_sync_completed_predictions(self) -> Dict[str, Any]:
        """
        Automatically sync any predictions that have outcomes but wrong status
        This can be called periodically to catch any missed syncs
        """
        if not self.enhanced_system:
            return {'synced': False, 'reason': 'Enhanced system not available'}
        
        try:
            updates_made = 0
            
            for pred in self.enhanced_system.contextual_predictions:
                if (pred.actual_winner and 
                    getattr(pred, 'match_status', None) in ['pending', 'draft', None]):
                    pred.match_status = 'completed'
                    updates_made += 1
            
            if updates_made > 0:
                self.enhanced_system.save_contextual_predictions()
            
            return {
                'synced': True,
                'predictions_updated': updates_made,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'synced': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }


# Global instance for easy access
sync_hooks = LearningSystemSyncHooks()


def install_sync_hooks():
    """
    Install synchronization hooks into the existing systems
    This modifies the existing classes to automatically sync
    """
    
    # Hook into PredictionTracker.update_prediction_outcome
    original_update_outcome = PredictionTracker.update_prediction_outcome
    
    def enhanced_update_outcome(self, record, actual_winner):
        # Call original method
        result = original_update_outcome(self, record, actual_winner)

        # Auto-complete AI predictions when outcome is recorded
        if AUTO_COMPLETION_AVAILABLE:
            auto_complete_result = auto_complete_on_outcome(record, actual_winner)
            if auto_complete_result.get('auto_completed'):
                print(f"🔄 Auto-completed prediction: {auto_complete_result.get('prediction_id', 'unknown')}")

        # Sync with enhanced system
        session_id = getattr(record, 'session_id', None)
        prediction_id = getattr(record, 'prediction_id', f"pred_{record.timestamp}")

        sync_result = sync_hooks.sync_prediction_outcome(
            prediction_id, actual_winner, session_id
        )

        if not sync_result['synced']:
            print(f"Warning: Failed to sync prediction outcome: {sync_result.get('error', 'Unknown error')}")

        return result
    
    # Replace the method
    PredictionTracker.update_prediction_outcome = enhanced_update_outcome
    
    # Hook into PredictionTracker.update_match_status
    original_update_status = PredictionTracker.update_match_status
    
    def enhanced_update_status(self, session_id, new_status):
        # Call original method
        result = original_update_status(self, session_id, new_status)
        
        # Sync with enhanced system
        sync_result = sync_hooks.sync_match_status_update(session_id, new_status)
        
        if not sync_result['synced']:
            print(f"Warning: Failed to sync match status: {sync_result.get('error', 'Unknown error')}")
        
        return result
    
    # Replace the method
    PredictionTracker.update_match_status = enhanced_update_status
    
    print("✅ Learning system synchronization hooks installed")


def uninstall_sync_hooks():
    """Remove synchronization hooks (for testing or debugging)"""
    # This would require storing original methods, but for now just notify
    print("⚠️  Sync hooks uninstall not implemented - restart application to remove hooks")


# Auto-install hooks when module is imported
try:
    install_sync_hooks()
except Exception as e:
    print(f"Warning: Could not install sync hooks: {e}")
