#!/usr/bin/env python3
"""
Cleanup Duplicate Predictions
Removes duplicate predictions from enhanced system to sync with main tracker
"""

import sys
sys.path.append('.')

def find_and_remove_duplicates():
    """Find and remove duplicate predictions from enhanced system"""
    print("🔍 FINDING AND REMOVING DUPLICATE PREDICTIONS")
    print("=" * 60)
    
    try:
        from enhanced_adaptive_learning_system import enhanced_learning_system
        from prediction_tracker import PredictionTracker
        
        tracker = PredictionTracker()
        
        # Get main tracker AI predictions for comparison
        main_ai_predictions = [p for p in tracker.predictions if getattr(p, 'is_ai_prediction', False)]
        
        print(f"📊 Main tracker AI predictions: {len(main_ai_predictions)}")
        print(f"📊 Enhanced system predictions: {len(enhanced_learning_system.contextual_predictions)}")
        
        # Create a set of main tracker predictions for comparison
        main_prediction_keys = set()
        for pred in main_ai_predictions:
            key = (tuple(pred.score), pred.set_number, pred.timestamp[:19])  # Compare without microseconds
            main_prediction_keys.add(key)
        
        print(f"\n🔍 Main tracker prediction keys:")
        for i, key in enumerate(sorted(main_prediction_keys)):
            print(f"   {i+1}. Score: {key[0]}, Set: {key[1]}, Time: {key[2]}")
        
        # Find enhanced predictions that don't match main tracker
        duplicates_to_remove = []
        enhanced_keys = []
        
        for pred in enhanced_learning_system.contextual_predictions:
            key = (tuple(pred.score), pred.set_number, pred.timestamp[:19])
            enhanced_keys.append(key)
            
            if key not in main_prediction_keys:
                duplicates_to_remove.append(pred)
                print(f"   ⚠️ Found orphaned prediction: {pred.prediction_id}")
                print(f"      Score: {pred.score}, Set: {pred.set_number}, Time: {pred.timestamp[:19]}")
        
        print(f"\n🔍 Enhanced system prediction keys:")
        for i, key in enumerate(sorted(enhanced_keys)):
            print(f"   {i+1}. Score: {key[0]}, Set: {key[1]}, Time: {key[2]}")
        
        # Check for duplicates within enhanced system
        enhanced_key_counts = {}
        for key in enhanced_keys:
            enhanced_key_counts[key] = enhanced_key_counts.get(key, 0) + 1
        
        internal_duplicates = {k: v for k, v in enhanced_key_counts.items() if v > 1}
        if internal_duplicates:
            print(f"\n⚠️ Found internal duplicates in enhanced system:")
            for key, count in internal_duplicates.items():
                print(f"   {key}: {count} copies")
        
        # Remove duplicates
        if duplicates_to_remove:
            print(f"\n🗑️ Removing {len(duplicates_to_remove)} orphaned predictions...")
            for pred in duplicates_to_remove:
                enhanced_learning_system.contextual_predictions.remove(pred)
                print(f"   Removed: {pred.prediction_id}")
            
            # Save the cleaned data
            enhanced_learning_system.save_contextual_predictions()
            print(f"✅ Saved cleaned enhanced system data")
            
            # Show updated count
            new_count = len(enhanced_learning_system.get_learning_eligible_predictions())
            print(f"📊 Updated enhanced system count: {new_count}")
        else:
            print("\n✅ No orphaned predictions found")
        
        return len(duplicates_to_remove)
        
    except Exception as e:
        print(f"❌ Error cleaning duplicates: {e}")
        import traceback
        traceback.print_exc()
        return 0

if __name__ == "__main__":
    print("🚀 DUPLICATE PREDICTION CLEANER")
    print("=" * 60)
    
    removed_count = find_and_remove_duplicates()
    
    if removed_count > 0:
        print(f"\n✅ Cleanup complete - removed {removed_count} duplicate(s)")
        print("Dashboard and console counts should now be synchronized!")
    else:
        print("\n✅ No cleanup needed - systems are already synchronized")
