#!/usr/bin/env python3
"""
Test Tournament Level Final Fix
Test the updated learning system integration to ensure it uses prediction metadata.
"""

import sys
sys.path.append('.')

def test_final_fix():
    """Test the final tournament level fix"""
    print("🧪 TESTING FINAL TOURNAMENT LEVEL FIX")
    print("=" * 50)
    
    try:
        from learning_system_integration import learning_integrator
        from prediction_tracker import PredictionTracker
        
        # Load current predictions
        tracker = PredictionTracker()
        ai_predictions = [p for p in tracker.predictions if getattr(p, 'is_ai_prediction', False)]
        
        if not ai_predictions:
            print("⚠️ No AI predictions found")
            return False
        
        # Test with the latest prediction
        latest_pred = ai_predictions[-1]
        print(f"🔍 Testing with: {latest_pred.player1_name} vs {latest_pred.player2_name}")
        
        # Check what data is available
        print(f"\n📋 Available Data:")
        learning_metadata = getattr(latest_pred, 'learning_metadata', {})
        context_factors = getattr(latest_pred, 'context_factors', {})
        
        print(f"   Learning metadata tournament_level: {learning_metadata.get('tournament_level', 'Not found')}")
        print(f"   Context factors tournament_level: {context_factors.get('match_context', {}).get('tournament_level', 'Not found')}")
        
        # Simulate the process_prediction_for_learning call
        print(f"\n🔄 Simulating process_prediction_for_learning...")
        
        result = learning_integrator.process_prediction_for_learning(
            prediction_record=latest_pred,
            tournament_name=learning_metadata.get('tournament_name', ''),
            additional_context={'tournament_level': 'ATP'}  # This should be ignored in favor of metadata
        )
        
        print(f"\n🎯 RESULT:")
        print(f"   Tournament level used: {result.get('tournament_level_used', 'Unknown')}")
        print(f"   Prediction added: {result.get('prediction_added', False)}")
        print(f"   Tournament classification: {result.get('tournament_classification', 'None')}")
        
        # Check if it's using the correct tournament level
        expected_level = learning_metadata.get('tournament_level', 'Unknown')
        actual_level = result.get('tournament_level_used', 'Unknown')
        
        if actual_level == expected_level:
            print(f"\n✅ SUCCESS! Using correct tournament level: {actual_level}")
            return True
        else:
            print(f"\n❌ PROBLEM! Expected: {expected_level}, Got: {actual_level}")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_log_message_simulation():
    """Simulate what the log message will show"""
    print("\n🧪 SIMULATING LOG MESSAGE")
    print("=" * 50)
    
    try:
        from learning_system_integration import enhanced_gui_integration
        from prediction_tracker import PredictionTracker
        
        # Load current predictions
        tracker = PredictionTracker()
        ai_predictions = [p for p in tracker.predictions if getattr(p, 'is_ai_prediction', False)]
        
        if not ai_predictions:
            print("⚠️ No AI predictions found")
            return False
        
        latest_pred = ai_predictions[-1]
        learning_metadata = getattr(latest_pred, 'learning_metadata', {})
        
        # Simulate the enhanced GUI integration call
        result = enhanced_gui_integration['record_prediction_outcome'](
            prediction_record=latest_pred,
            tournament_name=learning_metadata.get('tournament_name', ''),
            additional_context={'tournament_level': 'ATP'}
        )
        
        # Simulate the log message
        tournament_level_used = result.get('tournament_level_used', 'Unknown')
        prediction_added = result.get('prediction_added', False)
        
        print(f"📝 Log message will show:")
        print(f"   ✓ Enhanced learning: {tournament_level_used} - {prediction_added}")
        
        if tournament_level_used == 'ATP':
            print(f"\n🎉 SUCCESS! Log will show 'ATP' instead of 'Mixed'")
            return True
        else:
            print(f"\n⚠️ Still showing: {tournament_level_used}")
            return False
        
    except Exception as e:
        print(f"❌ Simulation failed: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 FINAL TOURNAMENT LEVEL FIX TEST")
    print("=" * 60)
    
    test1_passed = test_final_fix()
    test2_passed = test_log_message_simulation()
    
    print(f"\n📋 TEST RESULTS:")
    print(f"   Tournament Level Fix: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"   Log Message Simulation: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"The fix should now work correctly.")
        print(f"\n🔄 TO APPLY:")
        print(f"   1. Restart the tennis application")
        print(f"   2. Record another outcome")
        print(f"   3. You should see: '✓ Enhanced learning: ATP - True'")
    else:
        print(f"\n⚠️ SOME TESTS FAILED")
        print(f"Please review the output above for details.")


if __name__ == "__main__":
    main()
