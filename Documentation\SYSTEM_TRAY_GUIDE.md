# 🎾 System Tray Functionality Guide

## Overview

The automated tennis monitoring system now includes comprehensive system tray functionality, allowing the application to run unobtrusively in the background while maintaining full monitoring capabilities.

## Features

### ✅ System Tray Icon
- **Tennis Ball Icon**: Custom-designed green tennis ball with white seam lines
- **Tooltip**: Shows "🎾 Tennis Monitor" when hovering over the icon
- **Always Visible**: Icon remains in system tray while application is running

### ✅ Window Management
- **Minimize to Tray**: Clicking the window's close button (X) minimizes to tray instead of closing
- **Manual Minimize**: "Minimize to Tray" button in the Quick Actions section
- **Restore Window**: Double-click tray icon or use context menu to restore
- **Background Operation**: All monitoring continues while minimized to tray

### ✅ Context Menu
Right-click the system tray icon to access:
- **Show/Hide**: Toggle window visibility (default action)
- **Start Monitoring**: Begin tennis match monitoring from tray
- **Stop Monitoring**: Stop monitoring from tray
- **Exit**: Completely quit the application

### ✅ Preserved Functionality
- **2-second update intervals** continue while in tray
- **Desktop notifications** still work when minimized
- **Automatic draft creation** for tied matches continues
- **Live score monitoring** runs uninterrupted
- **Bet tracking** remains active

## Installation

### Prerequisites
```bash
# Install required dependencies
pip install pystray>=0.19.0
pip install Pillow>=9.0.0
```

### Automated Installation
```bash
# Run the installation script
python install_system_tray_deps.py
```

### Manual Installation
```bash
# Install from requirements file
pip install -r monitor_requirements.txt
```

## Usage

### Starting the Application
```bash
python monitor_gui.py
```

### Minimizing to Tray
1. **Automatic**: Click the window's close button (X)
2. **Manual**: Click "Minimize to Tray" button in Quick Actions
3. **Result**: Window disappears, tennis ball icon appears in system tray

### Restoring from Tray
1. **Double-click** the tennis ball icon in system tray
2. **Right-click** → "Show/Hide"
3. **Result**: Window reappears and comes to front

### Controlling from Tray
- **Start Monitoring**: Right-click → "Start Monitoring"
- **Stop Monitoring**: Right-click → "Stop Monitoring"
- **Exit Application**: Right-click → "Exit"

## Technical Implementation

### Key Components

#### System Tray Icon Creation
```python
def create_tennis_icon(self):
    """Create a simple tennis ball icon for the system tray"""
    # Creates 64x64 RGBA image with tennis ball design
    # Green circle with white curved seam lines
```

#### Window Management
```python
def on_window_close(self):
    """Handle window close event - minimize to tray instead of closing"""
    self.hide_to_tray()

def hide_to_tray(self):
    """Hide window to system tray"""
    self.root.withdraw()  # Hide the window
    # Start tray icon thread if not already running
```

#### Context Menu
```python
menu = pystray.Menu(
    item('Show/Hide', self.toggle_window, default=True),
    item('Start Monitoring', self.start_monitoring_from_tray),
    item('Stop Monitoring', self.stop_monitoring_from_tray),
    pystray.Menu.SEPARATOR,
    item('Exit', self.quit_application)
)
```

### Threading Model
- **Main Thread**: Tkinter GUI event loop
- **Tray Thread**: System tray icon management (daemon thread)
- **Monitor Thread**: Live score monitoring (existing functionality)

## Testing

### Test System Tray Functionality
```bash
# Run the test script
python test_system_tray.py
```

### Test Features
1. **Window Minimize**: Test minimize to tray functionality
2. **Icon Visibility**: Verify tennis ball icon appears in system tray
3. **Context Menu**: Test right-click menu options
4. **Restore Window**: Test window restoration from tray
5. **Notifications**: Test desktop notifications while minimized

## Troubleshooting

### Common Issues

#### System Tray Icon Not Appearing
- **Cause**: System tray disabled or hidden
- **Solution**: Check Windows system tray settings
- **Alternative**: Use "Show hidden icons" in system tray

#### Application Won't Close
- **Cause**: Tray icon thread still running
- **Solution**: Use "Exit" from tray context menu
- **Alternative**: End process from Task Manager

#### Window Won't Restore
- **Cause**: Window state corruption
- **Solution**: Right-click tray icon → "Show/Hide"
- **Alternative**: Restart application

### Dependencies Issues
```bash
# Reinstall dependencies if needed
pip uninstall pystray Pillow
pip install pystray>=0.19.0 Pillow>=9.0.0
```

## Benefits

### User Experience
- **Unobtrusive Operation**: Runs silently in background
- **Quick Access**: Easy window restoration from tray
- **Visual Indicator**: Tennis ball icon shows application status
- **No Taskbar Clutter**: Frees up taskbar space

### Monitoring Efficiency
- **Continuous Operation**: Monitoring never stops
- **Resource Efficient**: Minimal GUI overhead when minimized
- **Alert Delivery**: Notifications work regardless of window state
- **Workflow Integration**: Seamless with existing betting workflow

## Future Enhancements

### Potential Improvements
- **Status Indicators**: Different icon colors for monitoring states
- **Quick Stats**: Show match count in tooltip
- **Notification Settings**: Configure alerts from tray menu
- **Multiple Instances**: Support for multiple monitor instances

### Integration Opportunities
- **Enhanced GUI**: Direct launch from tray menu
- **Bet Management**: Quick bet actions from tray
- **Tournament Filters**: Quick filter changes from tray
- **Performance Metrics**: Monitor statistics in tooltip

## Conclusion

The system tray functionality transforms the tennis monitoring system into a true background service while maintaining full accessibility and control. Users can now run continuous monitoring without desktop clutter while having instant access to all features through the intuitive tray interface.
