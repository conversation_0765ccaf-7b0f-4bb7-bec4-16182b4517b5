# 🎾 Headless Mode Guide

## 🎉 **New Feature: Auto-Headless Mode**

Your tennis monitor now automatically switches to headless (background) mode after successful login! This means:

✅ **No browser window cluttering your screen**  
✅ **Monitoring runs silently in the background**  
✅ **Still get all alerts and notifications**  
✅ **Better performance and resource usage**

## 🔄 **How It Works**

### **Phase 1: Visible Login (2-3 seconds)**
1. 🌐 **<PERSON><PERSON><PERSON> opens visibly** for login verification
2. 🍪 **Cookies are loaded** and login status is tested
3. ✅ **Login success is confirmed** on the tennis page

### **Phase 2: Headless Transition (1-2 seconds)**
1. 💾 **Current session is saved** (cookies, page state)
2. 🔒 **Visible browser is closed**
3. 👻 **New headless browser is created** with same session
4. ✅ **Monitoring continues seamlessly** in background

### **Phase 3: Background Monitoring**
1. 🎾 **Scrapes live matches** every 2 seconds
2. 🚨 **Sends desktop alerts** for tied matches
3. 📊 **Updates GUI tables** with live data
4. 💾 **Saves data** and tracks bets

## ⚙️ **Configuration Options**

### **In the GUI (Settings Tab):**
- ✅ **"Auto-switch to headless mode after login"** (enabled by default)
- ❌ **Uncheck to keep browser visible** during monitoring

### **In Code (live_score_monitor.py):**
```python
# Enable/disable auto-headless mode
monitor.auto_headless = True   # Default: True
monitor.auto_headless = False  # Keep browser visible
```

## 🎯 **Benefits of Headless Mode**

### **🖥️ Screen Management:**
- **No browser window** taking up screen space
- **Clean desktop** while monitoring runs
- **Focus on other work** without distractions

### **⚡ Performance:**
- **Lower CPU usage** (no rendering)
- **Less memory consumption** (no GUI elements)
- **Faster scraping** (no visual updates)

### **🔒 Reliability:**
- **Less likely to be detected** as automated
- **No accidental clicks** or window interactions
- **Stable long-term monitoring**

## 🧪 **Testing the Feature**

### **Quick Test:**
```bash
python test_headless_mode.py
```

This will:
1. ✅ Open visible browser for login
2. ✅ Switch to headless mode
3. ✅ Test scraping functionality
4. ✅ Monitor for 30 seconds
5. ✅ Verify everything works

### **Manual Test:**
1. **Start monitor**: `python monitor_gui.py`
2. **Click "Start Monitoring"**
3. **Watch browser open** (visible for ~3 seconds)
4. **Browser disappears** (now running headless)
5. **Check "Live Matches" tab** for data
6. **Verify alerts work** when tied matches appear

## 🔧 **Troubleshooting**

### **If Headless Transition Fails:**
- ✅ **Monitor continues with visible browser**
- ✅ **Warning logged** but functionality preserved
- ✅ **All features still work normally**

### **If You Want Visible Browser:**
1. **Go to Settings tab**
2. **Uncheck "Auto-switch to headless mode"**
3. **Click "Apply Settings"**
4. **Restart monitoring**

### **Common Issues:**

#### **"Failed to switch to headless mode"**
- **Cause**: Session transfer issue
- **Solution**: Monitor continues with visible browser
- **Impact**: None - all features work normally

#### **"Headless browser may not be properly logged in"**
- **Cause**: Cookie transfer issue
- **Solution**: Check if alerts still work
- **Fix**: Restart monitor or disable auto-headless

#### **No matches found in headless mode**
- **Cause**: Page loading issue
- **Solution**: Increase wait times or disable headless
- **Check**: Look at monitor logs for errors

## 📋 **Best Practices**

### **🎯 Recommended Settings:**
- ✅ **Auto-headless: Enabled** (default)
- ✅ **Singles only: Enabled**
- ✅ **Tied scores only: Enabled**
- ✅ **Update interval: 2 seconds**

### **🔄 When to Disable Headless:**
- 🐛 **Debugging issues** with scraping
- 👀 **Want to see** what the browser is doing
- 🔧 **Testing new selectors** or functionality
- 📊 **Monitoring page changes** manually

### **💡 Pro Tips:**
- **Let the transition complete** (~5 seconds total)
- **Check logs** for any transition warnings
- **Test notifications** work in headless mode
- **Monitor CPU usage** - should be lower in headless

## 🚀 **What's Next**

### **Current Status:**
✅ **Headless mode implemented and tested**  
✅ **GUI controls added**  
✅ **Automatic transition working**  
✅ **Fallback to visible mode if needed**

### **Future Enhancements:**
- 🔄 **Auto-restart** if headless session fails
- 📊 **Performance monitoring** and optimization
- 🎛️ **Advanced headless options** (user agent, etc.)
- 📱 **Mobile-friendly** headless configurations

## 🎉 **Summary**

Your tennis monitor is now **much more user-friendly**:

1. **🌐 Opens browser briefly** for login verification
2. **👻 Switches to background mode** automatically  
3. **🎾 Monitors silently** without screen clutter
4. **🚨 Sends alerts** as normal
5. **📊 Updates GUI** with live data

**No more browser windows cluttering your screen while monitoring tennis matches!** 🎾✨

The monitor now runs like a **professional background service** while still providing all the functionality you need for tennis betting analysis.
