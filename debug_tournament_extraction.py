#!/usr/bin/env python3
"""
Debug Tournament Extraction
Debug exactly what's happening with the tournament level extraction.
"""

import sys
sys.path.append('.')

def debug_extraction():
    """Debug the exact extraction logic"""
    print("🔍 DEBUGGING TOURNAMENT EXTRACTION")
    print("=" * 50)
    
    try:
        from prediction_tracker import PredictionTracker
        
        # Load current predictions
        tracker = PredictionTracker()
        ai_predictions = [p for p in tracker.predictions if getattr(p, 'is_ai_prediction', False)]
        
        if not ai_predictions:
            print("⚠️ No AI predictions found")
            return
        
        # Test with the latest prediction
        ai_pred = ai_predictions[-1]
        print(f"🔍 Debugging prediction: {ai_pred.player1_name} vs {ai_pred.player2_name}")
        
        # Step by step debugging
        print(f"\n📋 STEP-BY-STEP DEBUGGING:")
        
        # Initial state
        tournament_level = 'Mixed'
        print(f"1. Initial tournament_level: '{tournament_level}'")
        
        # Check if learning_metadata exists
        has_learning_metadata = hasattr(ai_pred, 'learning_metadata')
        print(f"2. Has learning_metadata attribute: {has_learning_metadata}")
        
        if has_learning_metadata:
            learning_metadata = ai_pred.learning_metadata
            print(f"3. Learning metadata value: {learning_metadata}")
            print(f"4. Learning metadata type: {type(learning_metadata)}")
            
            if learning_metadata:
                print(f"5. Learning metadata is truthy: True")
                
                # Check if tournament_level key exists
                has_tournament_key = 'tournament_level' in learning_metadata
                print(f"6. Has 'tournament_level' key: {has_tournament_key}")
                
                if has_tournament_key:
                    extracted_value = learning_metadata['tournament_level']
                    print(f"7. Extracted value: '{extracted_value}'")
                    print(f"8. Extracted value type: {type(extracted_value)}")
                    
                    # Test the get() method
                    get_result = learning_metadata.get('tournament_level', tournament_level)
                    print(f"9. get() method result: '{get_result}'")
                    
                    # This is what the current code does
                    tournament_level = learning_metadata.get('tournament_level', tournament_level)
                    print(f"10. Final tournament_level: '{tournament_level}'")
                else:
                    print(f"6. No 'tournament_level' key found")
            else:
                print(f"5. Learning metadata is falsy")
        
        # Check context factors as well
        print(f"\n📋 CONTEXT FACTORS CHECK:")
        has_context_factors = hasattr(ai_pred, 'context_factors')
        print(f"1. Has context_factors: {has_context_factors}")
        
        if has_context_factors and ai_pred.context_factors:
            context_factors = ai_pred.context_factors
            match_context = context_factors.get('match_context', {})
            print(f"2. Match context: {match_context}")
            
            if 'tournament_level' in match_context:
                context_tournament_level = match_context['tournament_level']
                print(f"3. Context tournament level: '{context_tournament_level}'")
        
        print(f"\n🎯 FINAL RESULT: '{tournament_level}'")
        
        if tournament_level == 'ATP':
            print(f"✅ SUCCESS: Correctly extracted 'ATP'")
        elif tournament_level == 'Mixed':
            print(f"❌ PROBLEM: Still showing 'Mixed' despite ATP data being available")
        else:
            print(f"⚠️ UNEXPECTED: Got '{tournament_level}'")
        
        return tournament_level
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """Run the debug"""
    print("🚀 TOURNAMENT LEVEL EXTRACTION DEBUG")
    print("=" * 60)
    
    result = debug_extraction()
    
    print(f"\n📋 CONCLUSION:")
    if result == 'ATP':
        print(f"✅ Extraction is working correctly")
    elif result == 'Mixed':
        print(f"❌ There's still a bug in the extraction logic")
        print(f"💡 Need to investigate further")
    else:
        print(f"⚠️ Unexpected result: {result}")


if __name__ == "__main__":
    main()
