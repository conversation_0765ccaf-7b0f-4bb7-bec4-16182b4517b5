#!/usr/bin/env python3
"""
Test script to verify the updated red alert thresholds for auto-detect warnings.
Tests that alerts only show for truly complex and potentially embarrassing situations.
"""

def test_alert_threshold_logic():
    """Test the new alert threshold conditions"""
    
    print("🚨 Testing Updated Red Alert Thresholds")
    print("=" * 60)
    print("New criteria: Only alert for truly complex situations")
    print("- 4+ BPs with 2+ ADs")
    print("- 5+ BPs (regardless of ADs)")
    print("- 5+ ADs with 3+ BPs")
    print()
    
    test_cases = [
        # Cases that should NOT trigger alert (normal complexity)
        {
            'description': 'User\'s case: 1 AD, 3 BPs',
            'ad_count': 1,
            'bp_count': 3,
            'total_scores': 18,
            'should_alert': False,
            'reason': 'Not complex enough - only 3 BPs with 1 AD'
        },
        {
            'description': 'Moderate complexity: 2 ADs, 2 BPs',
            'ad_count': 2,
            'bp_count': 2,
            'total_scores': 12,
            'should_alert': False,
            'reason': 'Not complex enough - only 2 BPs'
        },
        {
            'description': 'Many ADs but few BPs: 4 ADs, 1 BP',
            'ad_count': 4,
            'bp_count': 1,
            'total_scores': 15,
            'should_alert': False,
            'reason': 'Not complex enough - only 1 BP'
        },
        {
            'description': 'Long game but simple: 0 ADs, 3 BPs',
            'ad_count': 0,
            'bp_count': 3,
            'total_scores': 20,
            'should_alert': False,
            'reason': 'Not complex enough - only 3 BPs'
        },
        
        # Cases that SHOULD trigger alert (very complex)
        {
            'description': 'Very complex: 4 BPs with 2 ADs',
            'ad_count': 2,
            'bp_count': 4,
            'total_scores': 18,
            'should_alert': True,
            'reason': 'Meets threshold: 4+ BPs with 2+ ADs'
        },
        {
            'description': 'Extremely complex: 5 BPs',
            'ad_count': 1,
            'bp_count': 5,
            'total_scores': 22,
            'should_alert': True,
            'reason': 'Meets threshold: 5+ BPs'
        },
        {
            'description': 'Very complex: 5 ADs with 3 BPs',
            'ad_count': 5,
            'bp_count': 3,
            'total_scores': 25,
            'should_alert': True,
            'reason': 'Meets threshold: 5+ ADs with 3+ BPs'
        },
        {
            'description': 'Borderline case: 4 BPs with 1 AD',
            'ad_count': 1,
            'bp_count': 4,
            'total_scores': 16,
            'should_alert': False,
            'reason': 'Not complex enough - needs 2+ ADs with 4+ BPs'
        }
    ]
    
    print("📋 Testing Alert Threshold Logic:")
    print()
    
    for i, test_case in enumerate(test_cases, 1):
        ad_count = test_case['ad_count']
        bp_count = test_case['bp_count']
        
        # Apply the new threshold logic
        if bp_count >= 4 and ad_count >= 2:
            has_issues = True
            alert_reason = f"4+ BPs ({bp_count}) with 2+ ADs ({ad_count})"
        elif bp_count >= 5:
            has_issues = True
            alert_reason = f"5+ BPs ({bp_count})"
        elif ad_count >= 5 and bp_count >= 3:
            has_issues = True
            alert_reason = f"5+ ADs ({ad_count}) with 3+ BPs ({bp_count})"
        else:
            has_issues = False
            alert_reason = "Below alert threshold"
        
        print(f"Test {i}: {test_case['description']}")
        print(f"  Stats: {ad_count} ADs, {bp_count} BPs, {test_case['total_scores']} total")
        print(f"  Alert triggered: {has_issues}")
        print(f"  Alert reason: {alert_reason}")
        print(f"  Expected alert: {test_case['should_alert']}")
        print(f"  Test reason: {test_case['reason']}")
        
        if has_issues == test_case['should_alert']:
            print(f"  ✅ PASS")
        else:
            print(f"  ❌ FAIL")
        
        print()

def test_real_world_scenarios():
    """Test with real-world tennis scenarios"""
    
    print("🎾 Testing Real-World Tennis Scenarios")
    print("=" * 60)
    
    scenarios = [
        {
            'name': 'Normal deuce game',
            'description': 'Standard deuce game with 1-2 break points',
            'ad_count': 2,
            'bp_count': 2,
            'expected_alert': False,
            'explanation': 'Common tennis scenario - should not alert'
        },
        {
            'name': 'Extended deuce battle',
            'description': 'Long deuce game with multiple advantages',
            'ad_count': 4,
            'bp_count': 2,
            'expected_alert': False,
            'explanation': 'Extended but not embarrassingly complex'
        },
        {
            'name': 'Marathon first game',
            'description': 'Extremely long first game with many break points',
            'ad_count': 3,
            'bp_count': 5,
            'expected_alert': True,
            'explanation': 'Embarrassingly complex - 5+ break points'
        },
        {
            'name': 'Epic deuce battle',
            'description': 'Historic long game with many advantages and breaks',
            'ad_count': 6,
            'bp_count': 4,
            'expected_alert': True,
            'explanation': 'Very complex - 6 ADs with 4 BPs'
        }
    ]
    
    for scenario in scenarios:
        ad_count = scenario['ad_count']
        bp_count = scenario['bp_count']
        
        # Apply threshold logic
        if bp_count >= 4 and ad_count >= 2:
            alert_triggered = True
        elif bp_count >= 5:
            alert_triggered = True
        elif ad_count >= 5 and bp_count >= 3:
            alert_triggered = True
        else:
            alert_triggered = False
        
        print(f"🎾 {scenario['name']}")
        print(f"   {scenario['description']}")
        print(f"   Stats: {ad_count} ADs, {bp_count} BPs")
        print(f"   Alert: {'🚨 YES' if alert_triggered else '✅ NO'}")
        print(f"   Expected: {'🚨 YES' if scenario['expected_alert'] else '✅ NO'}")
        print(f"   Explanation: {scenario['explanation']}")
        
        if alert_triggered == scenario['expected_alert']:
            print(f"   ✅ CORRECT")
        else:
            print(f"   ❌ INCORRECT")
        
        print()

if __name__ == "__main__":
    print("🚨 TESTING UPDATED RED ALERT THRESHOLDS")
    print("Testing that alerts only show for truly complex situations")
    print("This ensures users are only warned about potentially embarrassing cases")
    print()
    
    test_alert_threshold_logic()
    test_real_world_scenarios()
    
    print("=" * 60)
    print("🎯 Summary of New Alert Policy:")
    print("✅ Normal games (1-3 BPs, 1-4 ADs): No alert")
    print("✅ Extended deuce games: No alert")
    print("🚨 Very complex games (4+ BPs with 2+ ADs): Alert")
    print("🚨 Extreme games (5+ BPs or 5+ ADs with 3+ BPs): Alert")
    print()
    print("This ensures alerts only appear for truly problematic situations!")
