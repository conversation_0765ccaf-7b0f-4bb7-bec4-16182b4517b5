#!/usr/bin/env python3
"""
Debug script to understand why 66 predictions are learning-eligible when 70 are completed.
This will help identify what criteria are filtering out the 4 predictions.
"""

def debug_learning_eligibility():
    """Debug the difference between completed and learning-eligible predictions"""
    
    try:
        # Import the enhanced learning system
        from enhanced_adaptive_learning_system import enhanced_learning_system
        
        print("🔍 DEBUGGING LEARNING ELIGIBILITY")
        print("=" * 50)
        
        # Get all contextual predictions
        all_predictions = enhanced_learning_system.contextual_predictions
        print(f"📊 Total contextual predictions: {len(all_predictions)}")
        
        # Filter by those with actual winners (completed)
        completed_predictions = [p for p in all_predictions if p.actual_winner]
        print(f"✅ Predictions with actual winners: {len(completed_predictions)}")
        
        # Get learning eligible predictions
        learning_eligible = enhanced_learning_system.get_learning_eligible_predictions()
        print(f"🎯 Learning-eligible predictions: {len(learning_eligible)}")
        
        # Find the difference
        difference = len(completed_predictions) - len(learning_eligible)
        print(f"❓ Difference: {difference} predictions")
        
        if difference > 0:
            print(f"\n🔍 ANALYZING THE {difference} FILTERED OUT PREDICTIONS:")
            print("-" * 50)
            
            # Find predictions that have actual winners but are not learning eligible
            completed_ids = {p.prediction_id for p in completed_predictions}
            eligible_ids = {p.prediction_id for p in learning_eligible}
            filtered_out_ids = completed_ids - eligible_ids
            
            filtered_out_predictions = [p for p in all_predictions 
                                      if p.prediction_id in filtered_out_ids]
            
            for i, pred in enumerate(filtered_out_predictions, 1):
                print(f"\n{i}. Prediction ID: {pred.prediction_id}")
                print(f"   Timestamp: {pred.timestamp}")
                print(f"   Predicted: {pred.predicted_winner}")
                print(f"   Actual: {pred.actual_winner}")
                print(f"   Match Status: {getattr(pred, 'match_status', 'NOT SET')}")
                print(f"   Session ID: {getattr(pred, 'session_id', 'NOT SET')}")
                
                # Check specific eligibility criteria
                print(f"   Eligibility Check:")
                print(f"     - Has actual winner: {pred.actual_winner is not None}")
                
                match_status = getattr(pred, 'match_status', None)
                print(f"     - Match status: {match_status}")
                
                if match_status:
                    is_pending_or_draft = match_status in ["pending", "draft"]
                    is_completed = match_status == "completed"
                    print(f"     - Is pending/draft: {is_pending_or_draft}")
                    print(f"     - Is completed: {is_completed}")
                else:
                    print(f"     - No match status set (should be eligible)")
                
                # Check if it passes the eligibility function
                is_eligible = enhanced_learning_system.is_prediction_eligible_for_learning(pred)
                print(f"     - Overall eligible: {is_eligible}")
        
        print(f"\n📋 SUMMARY:")
        print(f"   Total predictions: {len(all_predictions)}")
        print(f"   With outcomes: {len(completed_predictions)}")
        print(f"   Learning eligible: {len(learning_eligible)}")
        print(f"   Filtered out: {difference}")
        
        # Check match status distribution
        print(f"\n📊 MATCH STATUS DISTRIBUTION:")
        status_counts = {}
        for pred in completed_predictions:
            status = getattr(pred, 'match_status', 'NOT SET')
            status_counts[status] = status_counts.get(status, 0) + 1
        
        for status, count in sorted(status_counts.items()):
            print(f"   {status}: {count}")
            
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_learning_eligibility()
