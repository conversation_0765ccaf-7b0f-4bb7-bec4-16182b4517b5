#!/usr/bin/env python3
"""
Fix Learning Eligibility System
Comprehensive solution to ensure AI predictions are properly marked as completed
and eligible for learning systems.
"""

import sys
from datetime import datetime
from typing import Dict, Any, List

# Add current directory to path
sys.path.append('.')

try:
    from prediction_tracker import PredictionTracker
    from enhanced_adaptive_learning_system import enhanced_learning_system
    from auto_completion_system import bulk_auto_complete_ai_predictions
    SYSTEMS_AVAILABLE = True
except ImportError as e:
    print(f"Error importing systems: {e}")
    SYSTEMS_AVAILABLE = False


def fix_existing_predictions() -> Dict[str, Any]:
    """Fix all existing AI predictions with outcomes that aren't marked as completed"""
    if not SYSTEMS_AVAILABLE:
        return {'error': 'Required systems not available'}
    
    print("🔧 FIXING EXISTING PREDICTIONS")
    print("=" * 50)
    
    results = {
        'main_system': {'processed': 0, 'fixed': 0},
        'enhanced_system': {'processed': 0, 'fixed': 0},
        'total_fixed': 0
    }
    
    # Fix main prediction system
    print("\n1️⃣ Fixing Main Prediction System...")
    tracker = PredictionTracker()
    main_result = bulk_auto_complete_ai_predictions(tracker.predictions)
    
    results['main_system']['processed'] = main_result['total_processed']
    results['main_system']['fixed'] = main_result['auto_completed']
    
    if main_result['auto_completed'] > 0:
        tracker.save_data()
        print(f"✅ Fixed {main_result['auto_completed']} predictions in main system")
    else:
        print("✅ No fixes needed in main system")
    
    # Fix enhanced learning system
    print("\n2️⃣ Fixing Enhanced Learning System...")
    enhanced_result = bulk_auto_complete_ai_predictions(enhanced_learning_system.contextual_predictions)
    
    results['enhanced_system']['processed'] = enhanced_result['total_processed']
    results['enhanced_system']['fixed'] = enhanced_result['auto_completed']
    
    if enhanced_result['auto_completed'] > 0:
        enhanced_learning_system.save_contextual_predictions()
        print(f"✅ Fixed {enhanced_result['auto_completed']} predictions in enhanced system")
    else:
        print("✅ No fixes needed in enhanced system")
    
    results['total_fixed'] = results['main_system']['fixed'] + results['enhanced_system']['fixed']
    
    return results


def verify_learning_eligibility() -> Dict[str, Any]:
    """Verify that learning systems can now access predictions properly"""
    if not SYSTEMS_AVAILABLE:
        return {'error': 'Required systems not available'}
    
    print("\n🔍 VERIFYING LEARNING ELIGIBILITY")
    print("=" * 50)
    
    # Check main system
    tracker = PredictionTracker()
    all_predictions = tracker.predictions
    ai_predictions = [p for p in all_predictions if getattr(p, 'is_ai_prediction', False)]
    ai_with_outcomes = [p for p in ai_predictions if p.actual_winner]
    ai_completed = [p for p in ai_with_outcomes if getattr(p, 'match_status', None) == 'completed']
    
    print(f"📊 Main System:")
    print(f"   Total predictions: {len(all_predictions)}")
    print(f"   AI predictions: {len(ai_predictions)}")
    print(f"   AI with outcomes: {len(ai_with_outcomes)}")
    print(f"   AI completed: {len(ai_completed)}")
    
    # Check enhanced system
    enhanced_predictions = enhanced_learning_system.contextual_predictions
    enhanced_with_outcomes = [p for p in enhanced_predictions if p.actual_winner]
    enhanced_ai = [p for p in enhanced_with_outcomes if getattr(p, 'is_ai_prediction', True)]  # Default to True
    enhanced_eligible = enhanced_learning_system.get_learning_eligible_predictions()
    
    print(f"\n📊 Enhanced System:")
    print(f"   Total predictions: {len(enhanced_predictions)}")
    print(f"   With outcomes: {len(enhanced_with_outcomes)}")
    print(f"   AI predictions: {len(enhanced_ai)}")
    print(f"   Learning eligible: {len(enhanced_eligible)}")
    
    # Check if optimization can run
    min_required = 10  # Minimum for optimization
    can_optimize = len(enhanced_eligible) >= min_required
    
    print(f"\n🎯 Learning Status:")
    print(f"   Can run optimization: {'✅ Yes' if can_optimize else '❌ No'}")
    print(f"   Eligible predictions: {len(enhanced_eligible)}")
    print(f"   Minimum required: {min_required}")
    
    return {
        'main_system': {
            'total': len(all_predictions),
            'ai_predictions': len(ai_predictions),
            'ai_with_outcomes': len(ai_with_outcomes),
            'ai_completed': len(ai_completed)
        },
        'enhanced_system': {
            'total': len(enhanced_predictions),
            'with_outcomes': len(enhanced_with_outcomes),
            'ai_predictions': len(enhanced_ai),
            'learning_eligible': len(enhanced_eligible)
        },
        'can_optimize': can_optimize,
        'timestamp': datetime.now().isoformat()
    }


def install_auto_completion_hooks():
    """Install automatic completion hooks for future predictions"""
    print("\n🔧 INSTALLING AUTO-COMPLETION HOOKS")
    print("=" * 50)
    
    try:
        from learning_system_sync_hooks import install_sync_hooks
        install_sync_hooks()
        print("✅ Auto-completion hooks installed successfully")
        print("   Future AI predictions will be automatically marked as completed")
        print("   when outcomes are recorded.")
        return True
    except Exception as e:
        print(f"❌ Failed to install hooks: {e}")
        return False


def main():
    """Main function to fix all learning eligibility issues"""
    print("🚀 LEARNING ELIGIBILITY FIX SYSTEM")
    print("=" * 60)
    print("This script will:")
    print("1. Fix existing AI predictions that should be marked as completed")
    print("2. Verify learning systems can access predictions properly")
    print("3. Install hooks to prevent future issues")
    print("=" * 60)
    
    if not SYSTEMS_AVAILABLE:
        print("❌ Required systems not available. Please check imports.")
        return
    
    # Step 1: Fix existing predictions
    fix_results = fix_existing_predictions()
    
    # Step 2: Verify everything is working
    verification = verify_learning_eligibility()
    
    # Step 3: Install hooks for future predictions
    hooks_installed = install_auto_completion_hooks()
    
    # Summary
    print("\n📋 SUMMARY")
    print("=" * 50)
    
    if 'error' not in fix_results:
        total_fixed = fix_results['total_fixed']
        print(f"✅ Fixed {total_fixed} existing predictions")
        
        if total_fixed > 0:
            print(f"   Main system: {fix_results['main_system']['fixed']} predictions")
            print(f"   Enhanced system: {fix_results['enhanced_system']['fixed']} predictions")
    
    if 'error' not in verification:
        eligible_count = verification['enhanced_system']['learning_eligible']
        can_optimize = verification['can_optimize']
        print(f"✅ Learning eligible predictions: {eligible_count}")
        print(f"✅ Can run optimization: {'Yes' if can_optimize else 'No'}")
    
    print(f"✅ Auto-completion hooks: {'Installed' if hooks_installed else 'Failed'}")
    
    print("\n🎯 NEXT STEPS:")
    print("1. Test the learning system optimization")
    print("2. Create new AI predictions and verify they auto-complete")
    print("3. Monitor the system for any future issues")
    
    if verification.get('can_optimize'):
        print("\n🚀 The learning system should now work properly!")
        print("   Try running optimization to verify everything is working.")
    else:
        print("\n⚠️  More AI predictions with outcomes may be needed for optimization.")


if __name__ == "__main__":
    main()
