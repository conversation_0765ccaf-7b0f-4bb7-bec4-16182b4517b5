# Selector Targets for BetsAPI Tennis Page

## 🎯 Visual Guide Based on Your Screenshot

Looking at your tennis live scores page, here are the exact elements to target:

### 1. Match Container (Each Row)
**Target**: Each complete horizontal row containing one match
- **Visual**: The entire row from "WTA Iasi" to the heart icons on the right
- **Likely selectors**: `tr`, `tbody tr`, `.match-row`

### 2. Tournament Name (Left Column)
**Target**: Tournament names like:
- "WTA Iasi"
- "WTA Rome WD" 
- "Challenger San Marino"
- "Challenger Granby"
- "ITF M25 Louisville"

**Visual**: Blue linked text in the leftmost column
**Likely selectors**: `td:first-child a`, `.tournament`, `td:nth-child(1) a`

### 3. Player 1 Name (Left Side of VS)
**Target**: First player names like:
- "<PERSON><PERSON>"
- "<PERSON>/<PERSON>"
- "<PERSON>"
- "<PERSON><PERSON>"

**Visual**: Player names on the left side of the match
**Likely selectors**: `.player1`, `td:nth-child(2)`, `.home-player`

### 4. Player 2 Name (Right Side of VS)
**Target**: Second player names like:
- "<PERSON><PERSON><PERSON>"
- "<PERSON>"
- "<PERSON><PERSON>"
- "<PERSON>x"

**Visual**: Player names on the right side of the match
**Likely selectors**: `.player2`, `td:nth-child(3)`, `.away-player`

### 5. Current Score (Blue Numbers)
**Target**: Scores like:
- "0-0" (tied matches - these are your priority!)
- "1-2"
- "4-2"
- "3-3"
- "A-40"
- "30-30"

**Visual**: Blue colored score text, usually in the middle-right area
**Likely selectors**: `.score`, `td:nth-child(4)`, `.current-score`

## 🔍 SelectorsHub Strategy

### Step 1: Start with Match Container
1. Right-click on any complete match row
2. Use SelectorsHub to get the row selector
3. This should select ALL match rows, not just one

### Step 2: Work Left to Right
1. Tournament (leftmost)
2. Player 1 (left of vs)
3. Player 2 (right of vs)  
4. Score (blue numbers)

### Step 3: Test Each Selector
In Chrome DevTools Console, test:
```javascript
document.querySelectorAll('YOUR_SELECTOR_HERE')
```
Should return multiple elements (one per match).

## 🎯 Priority Focus

**Most Important**: Focus on tied matches (0-0, 1-1, 2-2, etc.) as these trigger your alerts!

Look for matches with scores like:
- "0-0" ← High priority
- "1-1" ← High priority  
- "2-2" ← High priority
- "3-3" ← High priority
- "4-4" ← High priority
- "5-5" ← High priority

## 🚀 Quick Start

1. Run: `python manual_selector_input.py`
2. Follow the prompts to input your selectors
3. Test with: `python monitor_gui.py`
4. Check if tied matches are detected correctly
