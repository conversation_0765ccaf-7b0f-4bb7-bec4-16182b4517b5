#!/usr/bin/env python3
"""
Test Enhanced Learning Dashboard Updates
Verifies that the dashboard is using research-based sample size requirements
"""

def test_dashboard_updates():
    """Test that the dashboard uses updated sample size requirements"""
    print("🧪 Testing Enhanced Learning Dashboard Updates")
    print("=" * 50)
    
    # Test the updated validation readiness check
    try:
        from enhanced_learning_dashboard import check_validation_readiness
        
        print("📊 Testing validation readiness check...")
        readiness_info = check_validation_readiness()
        
        print(f"Status: {readiness_info['status']}")
        print(f"Message: {readiness_info['message']}")
        print(f"Color: {readiness_info['color']}")
        
        details = readiness_info.get('details', {})
        ai_predictions = details.get('ai_predictions', 0)
        min_required = details.get('min_required', 'unknown')
        
        print(f"AI predictions: {ai_predictions}")
        print(f"Min required: {min_required}")
        
        # Verify it's using the research-based minimum (200)
        if min_required == 200:
            print("✅ Dashboard is using research-based minimum (200)")
            success = True
        elif min_required == 50:
            print("❌ Dashboard still using old hardcoded minimum (50)")
            success = False
        else:
            print(f"⚠️ Dashboard using unexpected minimum ({min_required})")
            success = False
            
    except Exception as e:
        print(f"Error testing dashboard: {e}")
        success = False
    
    print("\n🎯 Expected behavior with current predictions:")
    print("   Message should show: 'Insufficient Data (X/200 AI predictions needed)'")
    print("   This reflects the research-based enhanced learning minimum of 200 samples")
    
    # Test enhanced learning system minimum
    print("\n🧠 Verifying Enhanced Learning System minimum:")
    try:
        from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem
        enhanced_system = EnhancedAdaptiveLearningSystem()
        system_min = enhanced_system.min_sample_size
        print(f"Enhanced system min_sample_size: {system_min}")
        
        if system_min == 200:
            print("✅ Enhanced system using research-based minimum (200)")
        else:
            print(f"⚠️ Enhanced system using unexpected minimum ({system_min})")
            
    except Exception as e:
        print(f"Error checking enhanced system: {e}")
    
    return success

def test_validation_display():
    """Test validation display updates"""
    print("\n🔬 Testing Validation Display Updates")
    print("-" * 40)
    
    # Test that validation results show correct minimums
    try:
        # Simulate insufficient data result
        mock_result = {
            'status': 'insufficient_data',
            'total_predictions': 99,
            'minimum_required': 200,  # Should be research-based value
            'message': 'Insufficient data for validation'
        }
        
        print("Mock validation result:")
        print(f"  Total predictions: {mock_result['total_predictions']}")
        print(f"  Minimum required: {mock_result['minimum_required']}")
        
        # Check if the fallback value would be correct
        fallback_min = mock_result.get('minimum_required', 200)  # Updated fallback
        if fallback_min == 200:
            print("✅ Validation display using research-based fallback (200)")
        else:
            print(f"❌ Validation display using incorrect fallback ({fallback_min})")
            
    except Exception as e:
        print(f"Error testing validation display: {e}")

def main():
    """Run all dashboard update tests"""
    print("🚀 ENHANCED LEARNING DASHBOARD UPDATE VERIFICATION")
    print("=" * 60)
    
    # Run tests
    dashboard_success = test_dashboard_updates()
    test_validation_display()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 SUMMARY")
    print("=" * 60)
    
    if dashboard_success:
        print("✅ Dashboard successfully updated with research-based requirements!")
        print("✅ No more hardcoded '50 AI predictions needed' messages")
        print("✅ Now shows '200 AI predictions needed' based on research")
        print("\n🎯 Impact:")
        print("   - More accurate feedback on data requirements")
        print("   - Aligned with enhanced learning system minimums")
        print("   - Prevents premature validation attempts")
    else:
        print("⚠️ Dashboard updates may need verification")
        print("Check the specific errors above")
    
    print(f"\n💡 With your current ~99 predictions:")
    print("   Dashboard will show: 'Insufficient Data (99/200 AI predictions needed)'")
    print("   This correctly reflects that you need 101 more predictions for enhanced learning")

if __name__ == "__main__":
    main()
