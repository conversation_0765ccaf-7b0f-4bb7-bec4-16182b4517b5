# 🎾 Live Tennis Score Monitor Setup Guide

## Overview
This system automates the monitoring of live tennis scores from betsapi.com and sends desktop alerts when tied matches are detected. It integrates seamlessly with your existing enhanced_gui.py for quick analysis.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r monitor_requirements.txt
```

### 2. Install Chrome WebDriver
The system uses Chrome WebDriver for web scraping. Install it using one of these methods:

**Option A: Automatic (Recommended)**
```bash
pip install webdriver-manager
```

**Option B: Manual**
- Download ChromeDriver from https://chromedriver.chromium.org/
- Add it to your system PATH

### 3. Find CSS Selectors
Before the monitor can work, we need to identify the correct CSS selectors for betsapi.com:

```bash
python selector_finder.py
```

Follow the interactive prompts to:
1. Login to betsapi.com manually
2. Inspect the page structure
3. Identify selectors for match data
4. Test the selectors

### 4. Start the Monitor
```bash
python monitor_gui.py
```

## 📋 System Components

### Core Files
- `live_score_monitor.py` - Main monitoring engine
- `monitor_gui.py` - Control panel GUI
- `integration_bridge.py` - Connects to enhanced_gui.py
- `selector_finder.py` - Helper for finding CSS selectors

### Data Files (Auto-created)
- `live_monitor_data/` - Directory for all monitor data
- `current_matches.json` - Current live matches
- `active_bets.json` - Your tracked bets
- `alert_history.json` - History of all alerts
- `monitor.log` - System logs

## 🎯 How It Works

### 1. Live Monitoring
- Checks betsapi.com every 2 seconds
- Filters for Challenger and ATP matches only
- Detects tied scores: 1-1, 2-2, 3-3, 4-4, 5-5, 6-6

### 2. Smart Alerts
- **New Tied Match**: Desktop notification for any new tied match
- **Bet Update**: Special alert for matches you're tracking
- **No Spam**: Won't alert for the same tied score twice

### 3. Bet Tracking
- Manually mark matches you've bet on
- Get alerts when those matches become tied again
- Track bet details and outcomes

### 4. Enhanced GUI Integration
- One-click launch of enhanced_gui.py
- Pre-fill match data from alerts
- Seamless workflow from alert → analysis → betting decision

## 🔧 Configuration

### Monitor Settings
- **Update Interval**: Default 2 seconds (configurable)
- **Target Tournaments**: Challenger, ATP (configurable)
- **Alert Types**: Desktop notifications only

### Browser Settings
- **Manual Login**: You login to betsapi.com manually
- **Session Persistence**: Browser stays open during monitoring
- **Headless Mode**: Disabled (so you can see the browser)

## 📱 Usage Workflow

### Initial Setup
1. Start `monitor_gui.py`
2. Click "Start Monitoring"
3. Login to betsapi.com in the opened browser
4. Monitor starts automatically after login

### Daily Workflow
1. **Monitor Running**: System watches for tied matches
2. **Alert Received**: Desktop notification for tied match
3. **Quick Analysis**: Click "Open Enhanced GUI" from monitor
4. **Add Bet Tracking**: Mark the match if you place a bet
5. **Re-analysis**: Get alerts when tracked matches tie again

### Bet Management
1. **Add Bet**: Use the "Bet Tracking" tab to add matches you've bet on
2. **Monitor Updates**: Get special alerts for your bet matches
3. **Remove Bet**: Remove tracking when bet is complete

## 🎮 Monitor GUI Features

### Monitor Control Tab
- Start/Stop monitoring
- View system status
- Monitor logs
- Quick actions (open enhanced GUI, test notifications)

### Live Matches Tab
- View all current live matches
- See tied matches highlighted
- Right-click to add bet tracking or open in enhanced GUI

### Bet Tracking Tab
- Add new bet tracking
- View all active bets
- Remove completed bets

### Alert History Tab
- View history of all alerts
- Track your response patterns

### Settings Tab
- Configure update intervals
- Set tournament filters
- Adjust alert preferences

## 🔍 CSS Selector Configuration

The system needs CSS selectors to extract match data from betsapi.com. Use `selector_finder.py` to identify these:

### Required Selectors
- **Match Container**: Selector for each match row
- **Tournament**: Tournament/competition name
- **Player 1**: First player name
- **Player 2**: Second player name
- **Score**: Current score

### Example Selectors (Update these based on actual site)
```json
{
  "match_container": "tr.match-row",
  "tournament": ".tournament-name",
  "player1": ".player1-name",
  "player2": ".player2-name",
  "score": ".current-score"
}
```

## 🚨 Troubleshooting

### Common Issues

**Monitor won't start**
- Check Chrome/ChromeDriver installation
- Ensure betsapi.com is accessible
- Check monitor.log for errors

**No alerts received**
- Verify CSS selectors are correct
- Check if matches are actually tied
- Test notifications with "Test Notification" button

**Browser closes unexpectedly**
- Check for Chrome updates
- Verify ChromeDriver compatibility
- Look for errors in monitor.log

**Enhanced GUI won't open**
- Ensure enhanced_gui.py is in the same directory
- Check Python path configuration
- Verify PyQt5 installation

### Debug Mode
Enable detailed logging by editing `live_score_monitor.py`:
```python
logging.basicConfig(level=logging.DEBUG)
```

## 🔒 Security Notes

- **Manual Login**: You login manually, no credentials stored
- **Local Data**: All data stored locally on your machine
- **No API Keys**: No external API keys required for monitoring
- **Browser Session**: Uses your existing browser session

## 🎯 Next Steps

### Phase 2 Enhancements (Future)
- **Telegram Integration**: Mobile alerts
- **Point-by-Point Data**: Integration with detailed score APIs
- **Auto-Bet Placement**: Full automation (requires betting API)
- **Machine Learning**: Predict which tied matches are most profitable

### Customization Options
- **Tournament Filters**: Add/remove specific tournaments
- **Score Filters**: Alert for specific score patterns
- **Time Windows**: Only monitor during certain hours
- **Multiple Betting Sites**: Monitor multiple sources

## 📞 Support

If you encounter issues:
1. Check the monitor.log file
2. Run selector_finder.py to verify selectors
3. Test with a simple match manually
4. Check browser console for JavaScript errors

## 🎉 Success Metrics

The system is working correctly when you see:
- ✅ Regular "Status: Running" in monitor GUI
- ✅ Live matches appearing in the matches tab
- ✅ Desktop notifications for tied matches
- ✅ Successful integration with enhanced_gui.py

---

**Ready to revolutionize your tennis betting workflow!** 🚀
