#!/usr/bin/env python3
"""
Test Dynamic Count Updates
Verify that learning-eligible counts update dynamically when recording outcomes.
"""

import sys
from datetime import datetime

# Add current directory to path
sys.path.append('.')

try:
    from enhanced_adaptive_learning_system import enhanced_learning_system, ContextualPredictionRecord
    from comprehensive_sync_manager import ComprehensiveSyncManager
    SYSTEMS_AVAILABLE = True
except ImportError as e:
    print(f"Error importing systems: {e}")
    SYSTEMS_AVAILABLE = False


def test_dynamic_count_updates():
    """Test that learning-eligible counts update when recording outcomes"""
    if not SYSTEMS_AVAILABLE:
        print("❌ Required systems not available")
        return False
    
    print("🧪 TESTING DYNAMIC COUNT UPDATES")
    print("=" * 50)
    
    # Get initial count
    initial_count = len(enhanced_learning_system.get_learning_eligible_predictions())
    print(f"📊 Initial learning-eligible count: {initial_count}")
    
    # Create a test prediction without outcome
    test_pred = ContextualPredictionRecord(
        prediction_id=f"test_dynamic_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        timestamp=datetime.now().isoformat(),
        set_number=1,
        score=(5, 5),
        surface="Hard",
        predicted_winner="TEST_A",
        match_status="pending",  # Starts as pending
        is_ai_prediction=True    # AI prediction
    )
    
    # Add to system
    enhanced_learning_system.contextual_predictions.append(test_pred)
    
    # Check count (should be same - no outcome yet)
    count_after_add = len(enhanced_learning_system.get_learning_eligible_predictions())
    print(f"📊 Count after adding pending prediction: {count_after_add}")
    
    if count_after_add != initial_count:
        print("❌ ERROR: Count changed when adding pending prediction!")
        return False
    
    # Record outcome (should auto-complete and increase count)
    print(f"\n🔄 Recording outcome for test prediction...")
    enhanced_learning_system.record_prediction_outcome(test_pred.prediction_id, "TEST_A")
    
    # Check count again (should be +1)
    count_after_outcome = len(enhanced_learning_system.get_learning_eligible_predictions())
    print(f"📊 Count after recording outcome: {count_after_outcome}")
    
    if count_after_outcome != initial_count + 1:
        print(f"❌ ERROR: Expected {initial_count + 1}, got {count_after_outcome}")
        return False
    
    # Test optimization log update
    print(f"\n🎯 Testing optimization log with new count...")
    
    # Simulate what happens in optimization
    all_predictions = enhanced_learning_system.contextual_predictions
    learning_eligible = [pred for pred in all_predictions
                        if enhanced_learning_system.is_prediction_eligible_for_learning(pred)]
    
    print(f"🎯 SIMULATED LOG: Using {len(learning_eligible)} learning-eligible predictions for optimization")
    
    if len(learning_eligible) != count_after_outcome:
        print(f"❌ ERROR: Optimization would use {len(learning_eligible)}, but count shows {count_after_outcome}")
        return False
    
    # Clean up test prediction
    enhanced_learning_system.contextual_predictions = [
        p for p in enhanced_learning_system.contextual_predictions 
        if p.prediction_id != test_pred.prediction_id
    ]
    enhanced_learning_system.save_contextual_predictions()
    
    # Verify cleanup
    final_count = len(enhanced_learning_system.get_learning_eligible_predictions())
    print(f"📊 Count after cleanup: {final_count}")
    
    if final_count != initial_count:
        print(f"❌ ERROR: Cleanup failed. Expected {initial_count}, got {final_count}")
        return False
    
    print(f"\n✅ ALL TESTS PASSED!")
    print(f"   ✅ Pending predictions don't affect count")
    print(f"   ✅ Recording outcomes increases count")
    print(f"   ✅ Auto-completion works correctly")
    print(f"   ✅ Optimization logs use fresh counts")
    print(f"   ✅ Cleanup works properly")
    
    return True


def test_sync_manager_integration():
    """Test that sync manager correctly reports counts"""
    if not SYSTEMS_AVAILABLE:
        return False
    
    print(f"\n🔄 TESTING SYNC MANAGER INTEGRATION")
    print("=" * 50)
    
    sync_manager = ComprehensiveSyncManager()
    
    # Get counts from sync manager
    counts = sync_manager.get_all_system_counts()
    
    # Get counts directly from systems
    enhanced_eligible = len(enhanced_learning_system.get_learning_eligible_predictions())
    
    print(f"📊 Sync Manager Enhanced Count: {counts['enhanced_system']['learning_eligible']}")
    print(f"📊 Direct Enhanced Count: {enhanced_eligible}")
    
    if counts['enhanced_system']['learning_eligible'] != enhanced_eligible:
        print(f"❌ ERROR: Sync manager count mismatch!")
        return False
    
    print(f"✅ Sync manager reports correct counts")
    return True


def main():
    """Run all dynamic count update tests"""
    print("🚀 DYNAMIC COUNT UPDATE TESTS")
    print("=" * 60)
    print("Testing that learning-eligible counts update properly")
    print("when predictions are added, outcomes recorded, and deletions occur.")
    print("=" * 60)
    
    if not SYSTEMS_AVAILABLE:
        print("❌ Required systems not available. Please check imports.")
        return
    
    # Test 1: Dynamic count updates
    test1_passed = test_dynamic_count_updates()
    
    # Test 2: Sync manager integration
    test2_passed = test_sync_manager_integration()
    
    # Summary
    print(f"\n📋 TEST SUMMARY")
    print("=" * 50)
    print(f"1. Dynamic Count Updates: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"2. Sync Manager Integration: {'✅ PASS' if test2_passed else '❌ FAIL'}")
    
    if test1_passed and test2_passed:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"\n🎯 WHAT THIS MEANS:")
        print(f"   ✅ Learning-eligible counts update immediately when outcomes are recorded")
        print(f"   ✅ Optimization logs will show current counts (not cached)")
        print(f"   ✅ Dashboard and console counts are synchronized")
        print(f"   ✅ Deletions are properly reflected across all systems")
        print(f"\n📝 NEXT STEPS:")
        print(f"   1. Record some new prediction outcomes in the GUI")
        print(f"   2. Check that the learning-eligible count increases")
        print(f"   3. Run optimization to see updated counts in logs")
        print(f"   4. Delete some predictions and verify counts decrease")
    else:
        print(f"\n⚠️  SOME TESTS FAILED - PLEASE INVESTIGATE")


if __name__ == "__main__":
    main()
