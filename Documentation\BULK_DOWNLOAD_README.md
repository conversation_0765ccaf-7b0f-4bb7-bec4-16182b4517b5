# ATP Players Bulk Download System

This system allows you to bulk download player profiles from Tennis Abstract for all ATP players listed in the `atp_players.csv` file.

## 🚀 Quick Start

### Option 1: GUI Interface (Recommended)
```bash
python bulk_download_gui.py
```

### Option 2: From Enhanced GUI
1. Run `python enhanced_gui.py`
2. Go to **Tools > Player Data > Bulk Download ATP Players**

### Option 3: Command Line
```bash
python bulk_player_downloader.py
```

## 📋 Features

### ✅ Core Functionality
- **Bulk Download**: Download all 65,991 ATP players from `atp_players.csv`
- **Smart Skipping**: Automatically skip players that already have profiles
- **Concurrent Downloads**: Configurable number of simultaneous downloads (default: 3)
- **Progress Tracking**: Real-time progress with statistics and ETA
- **Error Handling**: Robust error handling with detailed reporting
- **Resume Support**: Can resume interrupted downloads

### ⚡ Performance Optimizations
- **Fast Mode**: Optimized Chrome settings for bulk operations
- **Reduced Wait Times**: Shorter timeouts for bulk downloads
- **Connection Pooling**: Efficient WebDriver management
- **Rate Limiting**: Prevents overwhelming Tennis Abstract servers

### 📊 Monitoring & Reporting
- **Real-time Statistics**: Success/failure rates, download speed, ETA
- **Detailed Logs**: Complete download history with error messages
- **JSON Reports**: Exportable download reports for analysis
- **Progress Persistence**: Resume downloads from where they left off

## 🔧 Configuration Options

### Download Settings
- **Max Workers**: Number of concurrent downloads (1-10, recommended: 3)
- **Skip Existing**: Skip players that already have downloaded profiles
- **Max Players**: Limit number of players for testing (0 = all players)
- **Output Directory**: Where to save player profiles (default: "Players")

### Performance Settings
- **Fast Mode**: Automatically enabled for bulk operations
- **Wait Times**: Reduced for bulk downloads (2-5 seconds vs 5-15 seconds)
- **Chrome Options**: Optimized for speed and reliability

## 📁 File Structure

```
tennis-calculator-v4/
├── atp_players.csv              # Source data (65,991 ATP players)
├── bulk_player_downloader.py    # Core bulk download system
├── bulk_download_gui.py         # GUI interface
├── player_scraper.py           # Enhanced scraper (now with fast mode)
├── enhanced_gui.py             # Main app (now with bulk download menu)
├── test_bulk_download.py       # Test suite
├── Players/                    # Output directory for player profiles
└── download_report.json        # Download statistics and results
```

## 🧪 Testing

Run the test suite to verify everything works:
```bash
python test_bulk_download.py
```

The test suite checks:
- ✅ CSV parsing
- ✅ URL generation
- ✅ Existing player detection
- ✅ GUI module imports
- ✅ Integration with enhanced_gui.py
- ✅ Small download test (3 players)

## 📈 Expected Performance

### Download Statistics
- **Total Players**: 65,991 ATP players
- **Estimated Time**: 6-12 hours (depending on connection and server response)
- **Success Rate**: ~95% (some players may not have Tennis Abstract profiles)
- **File Size**: ~500MB total for all player profiles

### Rate Limiting
- **Concurrent Downloads**: 3 (recommended to avoid overwhelming server)
- **Request Interval**: ~2-5 seconds per player
- **Retry Logic**: Automatic retry for failed downloads

## 🔍 URL Pattern

The system generates Tennis Abstract URLs using this pattern:
```
https://www.tennisabstract.com/cgi-bin/player-classic.cgi?p={FirstName}{LastName}
```

Examples:
- Dominic Thiem → `DominicThiem`
- Rafael Nadal → `RafaelNadal`
- Novak Djokovic → `NovakDjokovic`

## 📊 CSV Format

The `atp_players.csv` file contains:
```csv
player_id,name_first,name_last,hand,dob,ioc,height,wikidata_id
100001,Gardnar,Mulloy,R,19131122,USA,185,Q54544
100002,Pancho,Segura,R,19210620,ECU,168,Q54581
...
```

## 🛠️ Integration with Enhanced GUI

The bulk download system is fully integrated with the main tennis calculator:

1. **Menu Access**: Tools > Player Data > Bulk Download ATP Players
2. **Automatic Detection**: Enhanced GUI automatically checks for missing players
3. **Seamless Integration**: Downloaded players immediately available for AI analysis
4. **Data Persistence**: All downloads are saved and tracked

## ⚠️ Important Notes

### Server Considerations
- **Rate Limiting**: Uses 3 concurrent downloads to avoid overwhelming Tennis Abstract
- **Respectful Scraping**: Includes appropriate delays between requests
- **Error Handling**: Graceful handling of server errors and timeouts

### Storage Requirements
- **Disk Space**: ~500MB for all player profiles
- **File Format**: Text files with organized player data
- **Naming Convention**: `{PlayerName}.txt` in the Players directory

### Dependencies
- **Selenium**: For web scraping
- **Chrome WebDriver**: Must be installed and in PATH
- **PyQt5**: For GUI interface
- **Python 3.7+**: Required for all functionality

## 🔧 Troubleshooting

### Common Issues

1. **Chrome WebDriver Not Found**
   ```bash
   # Install Chrome WebDriver
   # Download from: https://sites.google.com/chromium.org/driver/
   # Add to system PATH
   ```

2. **Selenium Import Error**
   ```bash
   pip install selenium beautifulsoup4 requests
   ```

3. **PyQt5 Import Error**
   ```bash
   pip install PyQt5
   ```

4. **CSV File Not Found**
   - Ensure `atp_players.csv` is in the same directory
   - Check file permissions

### Performance Issues

1. **Slow Downloads**
   - Reduce concurrent workers to 1-2
   - Check internet connection
   - Verify Chrome WebDriver version

2. **High Memory Usage**
   - Reduce batch size
   - Close other applications
   - Use fast mode (automatically enabled)

## 📞 Support

If you encounter issues:

1. **Run Tests**: `python test_bulk_download.py`
2. **Check Logs**: Review console output and error messages
3. **Verify Dependencies**: Ensure all required packages are installed
4. **Check File Permissions**: Ensure write access to output directory

## 🎯 Usage Examples

### Download All Players
```python
from bulk_player_downloader import BulkPlayerDownloader

downloader = BulkPlayerDownloader()
progress = downloader.bulk_download()
```

### Download First 100 Players
```python
progress = downloader.bulk_download(max_players=100)
```

### Custom Configuration
```python
downloader = BulkPlayerDownloader(
    csv_file="custom_players.csv",
    output_dir="custom_output"
)
progress = downloader.bulk_download(
    max_workers=5,
    skip_existing=False
)
```

## 🔄 Integration with AI Predictions

Once players are downloaded, they're automatically available for:
- ✅ AI-powered set predictions
- ✅ Historical match analysis
- ✅ Player performance statistics
- ✅ Surface-specific win rates
- ✅ Enhanced prediction accuracy

The system seamlessly integrates with the existing tennis calculator workflow!
