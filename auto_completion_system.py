"""
Auto-Completion System for Tennis Predictions
Automatically marks AI predictions as completed when outcomes are recorded.
"""

from typing import Optional, Dict, Any
from datetime import datetime


class AutoCompletionSystem:
    """
    Handles automatic completion of AI predictions when outcomes are recorded.
    Ensures only AI predictions are eligible for learning systems.
    """
    
    def __init__(self):
        self.enabled = True
        self.stats = {
            'auto_completed': 0,
            'skipped_math_predictions': 0,
            'already_completed': 0
        }
    
    def auto_complete_prediction_on_outcome(self, prediction_record, actual_winner: str) -> Dict[str, Any]:
        """
        Automatically mark AI predictions as completed when outcome is recorded.
        
        Args:
            prediction_record: The prediction record (PredictionRecord or ContextualPredictionRecord)
            actual_winner: The actual winner of the match
            
        Returns:
            Dict with completion status and details
        """
        if not self.enabled:
            return {'auto_completed': False, 'reason': 'auto_completion_disabled'}
        
        # Check if this is an AI prediction
        is_ai_prediction = getattr(prediction_record, 'is_ai_prediction', True)  # Default to True for backward compatibility
        
        if not is_ai_prediction:
            self.stats['skipped_math_predictions'] += 1
            return {
                'auto_completed': False, 
                'reason': 'not_ai_prediction',
                'prediction_type': 'mathematical'
            }
        
        # Check current status
        current_status = getattr(prediction_record, 'match_status', None)
        
        if current_status == 'completed':
            self.stats['already_completed'] += 1
            return {
                'auto_completed': False, 
                'reason': 'already_completed',
                'current_status': current_status
            }
        
        # Auto-complete the prediction
        if current_status in ['pending', 'draft', None]:
            prediction_record.match_status = 'completed'
            self.stats['auto_completed'] += 1
            
            prediction_id = getattr(prediction_record, 'prediction_id', 'unknown')
            print(f"🔄 Auto-completed AI prediction: {prediction_id} ({current_status or 'unknown'} → completed)")
            
            return {
                'auto_completed': True,
                'previous_status': current_status or 'unknown',
                'new_status': 'completed',
                'prediction_id': prediction_id,
                'timestamp': datetime.now().isoformat()
            }
        
        return {
            'auto_completed': False,
            'reason': 'unexpected_status',
            'current_status': current_status
        }
    
    def bulk_auto_complete_ai_predictions(self, prediction_list) -> Dict[str, Any]:
        """
        Auto-complete all AI predictions with outcomes that aren't already completed.
        
        Args:
            prediction_list: List of prediction records
            
        Returns:
            Dict with bulk completion results
        """
        results = {
            'total_processed': 0,
            'auto_completed': 0,
            'skipped_math': 0,
            'already_completed': 0,
            'no_outcome': 0,
            'completed_predictions': []
        }
        
        for pred in prediction_list:
            results['total_processed'] += 1
            
            # Skip predictions without outcomes
            if not getattr(pred, 'actual_winner', None):
                results['no_outcome'] += 1
                continue
            
            # Check if AI prediction
            is_ai_prediction = getattr(pred, 'is_ai_prediction', True)
            if not is_ai_prediction:
                results['skipped_math'] += 1
                continue
            
            # Check current status
            current_status = getattr(pred, 'match_status', None)
            if current_status == 'completed':
                results['already_completed'] += 1
                continue
            
            # Auto-complete
            if current_status in ['pending', 'draft', None]:
                pred.match_status = 'completed'
                results['auto_completed'] += 1
                
                prediction_id = getattr(pred, 'prediction_id', f"pred_{pred.timestamp if hasattr(pred, 'timestamp') else 'unknown'}")
                results['completed_predictions'].append({
                    'prediction_id': prediction_id,
                    'previous_status': current_status or 'unknown',
                    'predicted_winner': getattr(pred, 'predicted_winner', 'unknown'),
                    'actual_winner': pred.actual_winner
                })
        
        if results['auto_completed'] > 0:
            print(f"🔄 Bulk auto-completed {results['auto_completed']} AI predictions")
            for completed in results['completed_predictions'][:5]:  # Show first 5
                print(f"   ✅ {completed['prediction_id']}: {completed['previous_status']} → completed")
            if len(results['completed_predictions']) > 5:
                print(f"   ... and {len(results['completed_predictions']) - 5} more")
        
        return results
    
    def get_stats(self) -> Dict[str, Any]:
        """Get auto-completion statistics"""
        return {
            'stats': self.stats.copy(),
            'enabled': self.enabled,
            'timestamp': datetime.now().isoformat()
        }
    
    def reset_stats(self):
        """Reset statistics counters"""
        self.stats = {
            'auto_completed': 0,
            'skipped_math_predictions': 0,
            'already_completed': 0
        }
    
    def enable(self):
        """Enable auto-completion"""
        self.enabled = True
        print("✅ Auto-completion system enabled")
    
    def disable(self):
        """Disable auto-completion"""
        self.enabled = False
        print("⏸️ Auto-completion system disabled")


# Global instance for easy access
auto_completion_system = AutoCompletionSystem()


def auto_complete_on_outcome(prediction_record, actual_winner: str) -> Dict[str, Any]:
    """
    Convenience function to auto-complete a prediction when outcome is recorded.
    
    Args:
        prediction_record: The prediction record
        actual_winner: The actual winner
        
    Returns:
        Dict with completion status
    """
    return auto_completion_system.auto_complete_prediction_on_outcome(prediction_record, actual_winner)


def bulk_auto_complete_ai_predictions(prediction_list) -> Dict[str, Any]:
    """
    Convenience function to bulk auto-complete AI predictions.
    
    Args:
        prediction_list: List of prediction records
        
    Returns:
        Dict with bulk completion results
    """
    return auto_completion_system.bulk_auto_complete_ai_predictions(prediction_list)


if __name__ == "__main__":
    print("🔧 Auto-Completion System for Tennis Predictions")
    print("=" * 50)
    print("This system automatically marks AI predictions as completed")
    print("when outcomes are recorded, ensuring they're eligible for learning.")
    print()
    print("Key features:")
    print("- Only processes AI predictions (is_ai_prediction=True)")
    print("- Skips mathematical predictions")
    print("- Auto-marks predictions as 'completed' when outcomes are set")
    print("- Provides detailed statistics and logging")
    print()
    print("Usage:")
    print("  from auto_completion_system import auto_complete_on_outcome")
    print("  result = auto_complete_on_outcome(prediction_record, actual_winner)")
