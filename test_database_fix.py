#!/usr/bin/env python3
"""
Test script to verify the database error fix is working correctly.
This script tests the delete_prediction_by_criteria method in enhanced_adaptive_learning_system.py
"""

import sys
import os
from datetime import datetime
sys.path.append('.')

def test_enhanced_learning_system_deletion():
    """Test the fixed deletion method in enhanced_adaptive_learning_system.py"""
    print("🧪 TESTING ENHANCED LEARNING SYSTEM DELETION FIX")
    print("=" * 60)
    
    try:
        # Import the enhanced learning system
        from enhanced_adaptive_learning_system import enhanced_learning_system
        print("✅ Successfully imported enhanced_adaptive_learning_system")
        
        # Get initial count
        initial_count = len(enhanced_learning_system.contextual_predictions)
        print(f"📊 Initial prediction count: {initial_count}")
        
        # Test the delete method with dummy data (should not crash)
        test_score = (4, 4)
        test_set = 1
        test_timestamp = datetime.now().isoformat()
        
        print(f"🔍 Testing deletion with criteria:")
        print(f"   Score: {test_score}")
        print(f"   Set: {test_set}")
        print(f"   Timestamp: {test_timestamp}")
        
        # This should NOT produce a database error anymore
        deleted_count = enhanced_learning_system.delete_prediction_by_criteria(
            test_score, test_set, test_timestamp
        )
        
        print(f"✅ Deletion method executed successfully!")
        print(f"📊 Records deleted: {deleted_count}")
        
        # Verify final count
        final_count = len(enhanced_learning_system.contextual_predictions)
        print(f"📊 Final prediction count: {final_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_adaptive_learning_system_robustness():
    """Test the improved robustness check for adaptive_learning_system"""
    print("\n🔍 TESTING ADAPTIVE LEARNING SYSTEM ROBUSTNESS")
    print("=" * 60)
    
    try:
        from adaptive_learning_system import AdaptiveLearningSystem
        learning_system = AdaptiveLearningSystem()
        print("✅ Successfully imported AdaptiveLearningSystem")
        
        # Test if the method exists
        has_delete_method = hasattr(learning_system, 'delete_prediction_by_criteria')
        print(f"📋 Has delete_prediction_by_criteria method: {has_delete_method}")
        
        if has_delete_method:
            print("✅ Method exists - deletion would be attempted")
        else:
            print("ℹ️ Method does not exist - would skip deletion gracefully")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

def simulate_deletion_process():
    """Simulate the full deletion process to ensure no database errors"""
    print("\n🎭 SIMULATING FULL DELETION PROCESS")
    print("=" * 60)
    
    try:
        # Import all the systems that would be involved in deletion
        from enhanced_adaptive_learning_system import enhanced_learning_system
        from enhanced_adaptive_learning_v2 import enhanced_learning_system_v2
        from adaptive_learning_system import AdaptiveLearningSystem
        
        print("✅ All learning systems imported successfully")
        
        # Test data
        test_score = (6, 4)
        test_set = 2
        test_timestamp = datetime.now().isoformat()
        
        print(f"🔍 Simulating deletion for:")
        print(f"   Score: {test_score}")
        print(f"   Set: {test_set}")
        print(f"   Timestamp: {test_timestamp}")
        
        # Test Enhanced Learning System (the one we fixed)
        print("\n1️⃣ Testing Enhanced Learning System...")
        try:
            deleted_count_1 = enhanced_learning_system.delete_prediction_by_criteria(
                test_score, test_set, test_timestamp
            )
            print(f"   ✅ Enhanced Learning System: {deleted_count_1} record(s) removed")
        except Exception as e:
            print(f"   ❌ Enhanced Learning System error: {e}")
            return False
        
        # Test Enhanced Learning System V2
        print("\n2️⃣ Testing Enhanced Learning System V2...")
        try:
            deleted_count_2 = enhanced_learning_system_v2.delete_prediction_by_criteria(
                test_score, test_set, test_timestamp
            )
            print(f"   ✅ Enhanced Learning V2: {deleted_count_2} record(s) removed")
        except Exception as e:
            print(f"   ❌ Enhanced Learning V2 error: {e}")
            return False
        
        # Test Adaptive Learning System (with robustness check)
        print("\n3️⃣ Testing Adaptive Learning System...")
        try:
            learning_system = AdaptiveLearningSystem()
            if hasattr(learning_system, 'delete_prediction_by_criteria'):
                deleted_count_3 = learning_system.delete_prediction_by_criteria(
                    test_score, test_set, test_timestamp
                )
                print(f"   ✅ Adaptive Learning System: {deleted_count_3} record(s) removed")
            else:
                print(f"   ℹ️ Adaptive Learning System: JSON-only, no deletion method required.")
        except Exception as e:
            print(f"   ❌ Adaptive Learning System error: {e}")
            return False
        
        print("\n🎉 ALL DELETION TESTS PASSED!")
        print("No database errors encountered.")
        return True
        
    except Exception as e:
        print(f"❌ Error during simulation: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 DATABASE ERROR FIX VERIFICATION")
    print("=" * 70)
    
    # Test 1: Enhanced learning system deletion
    test1_passed = test_enhanced_learning_system_deletion()
    
    # Test 2: Adaptive learning system robustness
    test2_passed = test_adaptive_learning_system_robustness()
    
    # Test 3: Full deletion simulation
    test3_passed = simulate_deletion_process()
    
    print("\n📋 FINAL RESULTS")
    print("=" * 30)
    print(f"Enhanced system deletion test: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Adaptive system robustness test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    print(f"Full deletion simulation test: {'✅ PASSED' if test3_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed and test3_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("The database error fix has been successfully implemented.")
        print("\nThe 'no such table: contextual_predictions' error should no longer occur.")
        print("Prediction deletions will now work correctly across all systems.")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("Please review the implementation and fix any remaining issues.")
