#!/usr/bin/env python3
"""
Analyze Database Schemas
Understand what data is stored in each database and identify redundancies.
"""

import sqlite3
import json
from pathlib import Path
from typing import Dict, Any, List


def analyze_enhanced_learning_db():
    """Analyze the enhanced learning database"""
    db_path = Path("enhanced_learning_data/enhanced_learning.db")
    
    if not db_path.exists():
        return {"status": "not_found", "tables": []}
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # Get all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            table_info = {}
            for table in tables:
                # Get table schema
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                
                # Get row count
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                row_count = cursor.fetchone()[0]
                
                # Get sample data
                cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                sample_data = cursor.fetchall()
                
                table_info[table] = {
                    "columns": columns,
                    "row_count": row_count,
                    "sample_data": sample_data
                }
            
            return {"status": "found", "tables": tables, "table_info": table_info}
            
    except Exception as e:
        return {"status": "error", "error": str(e)}


def analyze_adaptive_learning_db():
    """Analyze the adaptive learning database"""
    db_path = Path("learning_data/learning_database.db")
    
    if not db_path.exists():
        return {"status": "not_found", "tables": []}
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # Get all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            table_info = {}
            for table in tables:
                # Get table schema
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                
                # Get row count
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                row_count = cursor.fetchone()[0]
                
                # Get sample data
                cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                sample_data = cursor.fetchall()
                
                table_info[table] = {
                    "columns": columns,
                    "row_count": row_count,
                    "sample_data": sample_data
                }
            
            return {"status": "found", "tables": tables, "table_info": table_info}
            
    except Exception as e:
        return {"status": "error", "error": str(e)}


def analyze_json_files():
    """Analyze JSON prediction files"""
    files_info = {}
    
    # Main prediction history
    main_file = Path("prediction_history.json")
    if main_file.exists():
        try:
            with open(main_file, 'r') as f:
                data = json.load(f)
                files_info["prediction_history.json"] = {
                    "count": len(data),
                    "sample_fields": list(data[0].keys()) if data else [],
                    "sample_record": data[0] if data else None
                }
        except Exception as e:
            files_info["prediction_history.json"] = {"error": str(e)}
    
    # Enhanced contextual predictions
    enhanced_file = Path("enhanced_learning_data/contextual_predictions.json")
    if enhanced_file.exists():
        try:
            with open(enhanced_file, 'r') as f:
                data = json.load(f)
                files_info["contextual_predictions.json"] = {
                    "count": len(data),
                    "sample_fields": list(data[0].keys()) if data else [],
                    "sample_record": data[0] if data else None
                }
        except Exception as e:
            files_info["contextual_predictions.json"] = {"error": str(e)}
    
    return files_info


def compare_data_structures():
    """Compare data structures between systems"""
    print("🔍 ANALYZING DATABASE SCHEMAS AND DATA STRUCTURES")
    print("=" * 60)
    
    # Analyze databases
    enhanced_db = analyze_enhanced_learning_db()
    adaptive_db = analyze_adaptive_learning_db()
    json_files = analyze_json_files()
    
    print("\n1️⃣ ENHANCED LEARNING DATABASE")
    print("-" * 40)
    if enhanced_db["status"] == "found":
        print(f"📊 Tables: {enhanced_db['tables']}")
        for table, info in enhanced_db["table_info"].items():
            print(f"\n📋 Table: {table}")
            print(f"   Rows: {info['row_count']}")
            print(f"   Columns: {[col[1] for col in info['columns']]}")
    else:
        print(f"❌ Status: {enhanced_db['status']}")
    
    print("\n2️⃣ ADAPTIVE LEARNING DATABASE")
    print("-" * 40)
    if adaptive_db["status"] == "found":
        print(f"📊 Tables: {adaptive_db['tables']}")
        for table, info in adaptive_db["table_info"].items():
            print(f"\n📋 Table: {table}")
            print(f"   Rows: {info['row_count']}")
            print(f"   Columns: {[col[1] for col in info['columns']]}")
    else:
        print(f"❌ Status: {adaptive_db['status']}")
    
    print("\n3️⃣ JSON FILES")
    print("-" * 40)
    for filename, info in json_files.items():
        print(f"\n📄 File: {filename}")
        if "error" in info:
            print(f"   ❌ Error: {info['error']}")
        else:
            print(f"   Records: {info['count']}")
            print(f"   Fields: {info['sample_fields'][:5]}...")  # Show first 5 fields
    
    print("\n4️⃣ DATA OVERLAP ANALYSIS")
    print("-" * 40)
    
    # Check for overlapping data
    main_count = json_files.get("prediction_history.json", {}).get("count", 0)
    enhanced_count = json_files.get("contextual_predictions.json", {}).get("count", 0)
    enhanced_db_count = 0
    adaptive_db_count = 0
    
    if enhanced_db["status"] == "found" and "contextual_predictions" in enhanced_db["table_info"]:
        enhanced_db_count = enhanced_db["table_info"]["contextual_predictions"]["row_count"]
    
    if adaptive_db["status"] == "found" and "weight_performance" in adaptive_db["table_info"]:
        adaptive_db_count = adaptive_db["table_info"]["weight_performance"]["row_count"]
    
    print(f"📊 Data Counts:")
    print(f"   Main JSON (prediction_history.json): {main_count} records")
    print(f"   Enhanced JSON (contextual_predictions.json): {enhanced_count} records")
    print(f"   Enhanced DB (contextual_predictions table): {enhanced_db_count} records")
    print(f"   Adaptive DB (weight_performance table): {adaptive_db_count} records")
    
    # Identify redundancies
    print(f"\n🔍 REDUNDANCY ANALYSIS:")
    
    if enhanced_count > 0 and enhanced_db_count > 0:
        if abs(enhanced_count - enhanced_db_count) < 5:
            print(f"   ⚠️ REDUNDANCY: Enhanced system stores same data in JSON + DB")
            print(f"      JSON: {enhanced_count} records, DB: {enhanced_db_count} records")
        else:
            print(f"   ✅ Enhanced JSON and DB have different counts (may be intentional)")
    
    if main_count > 0 and enhanced_count > 0:
        overlap_ratio = min(main_count, enhanced_count) / max(main_count, enhanced_count)
        if overlap_ratio > 0.8:
            print(f"   ⚠️ POTENTIAL OVERLAP: Main and Enhanced systems have similar record counts")
            print(f"      Main: {main_count}, Enhanced: {enhanced_count} (ratio: {overlap_ratio:.2f})")
        else:
            print(f"   ✅ Main and Enhanced systems have different data volumes")
    
    print(f"\n5️⃣ RECOMMENDATIONS")
    print("-" * 40)
    
    recommendations = []
    
    # Check for JSON + DB redundancy
    if enhanced_count > 0 and enhanced_db_count > 0 and abs(enhanced_count - enhanced_db_count) < 5:
        recommendations.append("🔄 Enhanced system stores same data in both JSON and SQLite - consider using only one")
    
    # Check for different purposes
    if adaptive_db_count > 0 and enhanced_db_count > 0:
        recommendations.append("🔍 Two separate databases exist - verify they serve different purposes")
    
    # Check data structure differences
    main_fields = json_files.get("prediction_history.json", {}).get("sample_fields", [])
    enhanced_fields = json_files.get("contextual_predictions.json", {}).get("sample_fields", [])
    
    if main_fields and enhanced_fields:
        common_fields = set(main_fields) & set(enhanced_fields)
        unique_main = set(main_fields) - set(enhanced_fields)
        unique_enhanced = set(enhanced_fields) - set(main_fields)
        
        print(f"\n📋 FIELD COMPARISON:")
        print(f"   Common fields: {len(common_fields)}")
        print(f"   Main-only fields: {len(unique_main)} - {list(unique_main)[:3]}...")
        print(f"   Enhanced-only fields: {len(unique_enhanced)} - {list(unique_enhanced)[:3]}...")
        
        if len(unique_enhanced) > 3:
            recommendations.append("✅ Enhanced system has unique fields - justified separate storage")
        else:
            recommendations.append("🤔 Enhanced system has few unique fields - consider consolidation")
    
    if recommendations:
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. {rec}")
    else:
        print("   ✅ No obvious redundancies detected")
    
    return {
        "enhanced_db": enhanced_db,
        "adaptive_db": adaptive_db,
        "json_files": json_files,
        "recommendations": recommendations
    }


if __name__ == "__main__":
    compare_data_structures()
