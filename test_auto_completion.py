#!/usr/bin/env python3
"""
Test Auto-Completion System
Verify that new AI predictions are automatically marked as completed when outcomes are recorded.
"""

import sys
from datetime import datetime

# Add current directory to path
sys.path.append('.')

try:
    from prediction_tracker import PredictionTracker, PredictionRecord
    from enhanced_adaptive_learning_system import enhanced_learning_system, ContextualPredictionRecord
    from auto_completion_system import auto_complete_on_outcome
    SYSTEMS_AVAILABLE = True
except ImportError as e:
    print(f"Error importing systems: {e}")
    SYSTEMS_AVAILABLE = False


def test_main_system_auto_completion():
    """Test auto-completion in the main prediction system"""
    print("🧪 Testing Main System Auto-Completion")
    print("-" * 40)
    
    # Create a test AI prediction
    test_pred = PredictionRecord(
        timestamp=datetime.now().isoformat(),
        score=(3, 3),
        player1_name="Test Player A",
        player2_name="Test Player B",
        player1_code="TPA",
        player2_code="TPB",
        predicted_winner="TPA",
        prediction_probability=0.65,
        confidence=0.7,
        is_ai_prediction=True,  # This is an AI prediction
        match_status="pending"  # Starts as pending
    )
    
    print(f"✅ Created test AI prediction:")
    print(f"   Status: {test_pred.match_status}")
    print(f"   AI Prediction: {test_pred.is_ai_prediction}")
    print(f"   Actual Winner: {test_pred.actual_winner}")
    
    # Record the outcome first (simulating what happens in the GUI)
    test_pred.actual_winner = "TPA"

    # Test auto-completion when outcome is recorded
    result = auto_complete_on_outcome(test_pred, "TPA")

    print(f"\n🔄 Auto-completion result:")
    print(f"   Auto-completed: {result.get('auto_completed', False)}")
    print(f"   New status: {test_pred.match_status}")
    print(f"   Actual winner: {test_pred.actual_winner}")
    
    # Verify it's now eligible for learning
    tracker = PredictionTracker()
    is_eligible = tracker.is_prediction_eligible_for_learning(test_pred)
    print(f"   Learning eligible: {is_eligible}")
    
    return result.get('auto_completed', False) and is_eligible


def test_enhanced_system_auto_completion():
    """Test auto-completion in the enhanced learning system"""
    print("\n🧪 Testing Enhanced System Auto-Completion")
    print("-" * 40)
    
    # Create a test contextual prediction
    test_pred = ContextualPredictionRecord(
        prediction_id=f"test_pred_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        timestamp=datetime.now().isoformat(),
        set_number=1,
        score=(4, 4),
        surface="Hard",
        predicted_winner="TPC",
        match_status="pending",  # Starts as pending
        is_ai_prediction=True   # This is an AI prediction
    )
    
    print(f"✅ Created test contextual prediction:")
    print(f"   Status: {test_pred.match_status}")
    print(f"   AI Prediction: {getattr(test_pred, 'is_ai_prediction', 'Not set')}")
    print(f"   Actual Winner: {test_pred.actual_winner}")
    
    # Record the outcome first (simulating what happens in the GUI)
    test_pred.actual_winner = "TPC"

    # Test auto-completion when outcome is recorded
    result = auto_complete_on_outcome(test_pred, "TPC")

    print(f"\n🔄 Auto-completion result:")
    print(f"   Auto-completed: {result.get('auto_completed', False)}")
    print(f"   New status: {test_pred.match_status}")
    print(f"   Actual winner: {test_pred.actual_winner}")
    
    # Verify it's now eligible for learning
    is_eligible = enhanced_learning_system.is_prediction_eligible_for_learning(test_pred)
    print(f"   Learning eligible: {is_eligible}")
    
    return result.get('auto_completed', False) and is_eligible


def test_math_prediction_filtering():
    """Test that mathematical predictions are NOT auto-completed"""
    print("\n🧪 Testing Mathematical Prediction Filtering")
    print("-" * 40)
    
    # Create a test mathematical prediction
    test_pred = PredictionRecord(
        timestamp=datetime.now().isoformat(),
        score=(5, 5),
        player1_name="Test Player C",
        player2_name="Test Player D",
        player1_code="TPC",
        player2_code="TPD",
        predicted_winner="TPC",
        prediction_probability=0.55,
        confidence=0.6,
        is_ai_prediction=False,  # This is a MATHEMATICAL prediction
        match_status="pending"
    )
    
    print(f"✅ Created test mathematical prediction:")
    print(f"   Status: {test_pred.match_status}")
    print(f"   AI Prediction: {test_pred.is_ai_prediction}")
    print(f"   Actual Winner: {test_pred.actual_winner}")
    
    # Record the outcome first (simulating what happens in the GUI)
    test_pred.actual_winner = "TPC"

    # Test auto-completion - should NOT complete mathematical predictions
    result = auto_complete_on_outcome(test_pred, "TPC")

    print(f"\n🔄 Auto-completion result:")
    print(f"   Auto-completed: {result.get('auto_completed', False)}")
    print(f"   Status unchanged: {test_pred.match_status}")
    print(f"   Reason: {result.get('reason', 'Unknown')}")
    
    # Verify it's NOT eligible for learning (mathematical predictions shouldn't be)
    tracker = PredictionTracker()
    is_eligible = tracker.is_prediction_eligible_for_learning(test_pred)
    print(f"   Learning eligible: {is_eligible}")
    
    return not result.get('auto_completed', True) and result.get('reason') == 'not_ai_prediction'


def main():
    """Run all auto-completion tests"""
    print("🚀 AUTO-COMPLETION SYSTEM TESTS")
    print("=" * 50)
    print("Testing that AI predictions are automatically marked as completed")
    print("when outcomes are recorded, while mathematical predictions are not.")
    print("=" * 50)
    
    if not SYSTEMS_AVAILABLE:
        print("❌ Required systems not available. Please check imports.")
        return
    
    # Run tests
    test_results = []
    
    # Test 1: Main system auto-completion
    test_results.append(test_main_system_auto_completion())
    
    # Test 2: Enhanced system auto-completion
    test_results.append(test_enhanced_system_auto_completion())
    
    # Test 3: Mathematical prediction filtering
    test_results.append(test_math_prediction_filtering())
    
    # Summary
    print("\n📋 TEST RESULTS")
    print("=" * 50)
    
    test_names = [
        "Main System Auto-Completion",
        "Enhanced System Auto-Completion", 
        "Mathematical Prediction Filtering"
    ]
    
    all_passed = True
    for i, (name, passed) in enumerate(zip(test_names, test_results)):
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{i+1}. {name}: {status}")
        if not passed:
            all_passed = False
    
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🚀 Auto-completion system is working correctly!")
        print("   - AI predictions will be automatically marked as completed")
        print("   - Mathematical predictions will be ignored")
        print("   - Learning systems will have access to completed AI predictions")
    else:
        print("\n⚠️  Some tests failed. Please check the auto-completion system.")


if __name__ == "__main__":
    main()
