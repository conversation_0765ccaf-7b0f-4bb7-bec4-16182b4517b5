#!/usr/bin/env python3
"""
Debug why deletion from contextual_predictions.json is not working
"""

def debug_deletion_issue():
    """Debug why the enhanced learning system deletion is not working"""
    print("🔍 DEBUGGING DELETION ISSUE")
    print("=" * 50)
    
    try:
        from enhanced_adaptive_learning_system import enhanced_learning_system
        from prediction_tracker import PredictionTracker
        
        # Check current state
        print("\n1️⃣ Current State:")
        enhanced_predictions = enhanced_learning_system.contextual_predictions
        print(f"   Enhanced system predictions: {len(enhanced_predictions)}")
        
        main_tracker = PredictionTracker()
        main_predictions = main_tracker.predictions
        print(f"   Main tracker predictions: {len(main_predictions)}")
        
        # Show details of enhanced predictions
        print("\n2️⃣ Enhanced System Predictions:")
        for i, pred in enumerate(enhanced_predictions):
            print(f"   Prediction {i+1}:")
            print(f"     Score: {pred.score}")
            print(f"     Set: {pred.set_number}")
            print(f"     Timestamp: {pred.timestamp}")
            print(f"     Predicted: {pred.predicted_winner}")
            print(f"     Actual: {pred.actual_winner}")
            print()
        
        # Show details of main tracker predictions
        print("3️⃣ Main Tracker Predictions:")
        for i, pred in enumerate(main_predictions):
            print(f"   Prediction {i+1}:")
            print(f"     Score: {pred.score}")
            print(f"     Set: {pred.set_number}")
            print(f"     Timestamp: {pred.timestamp}")
            print(f"     Predicted: {pred.predicted_winner}")
            print(f"     Actual: {getattr(pred, 'actual_winner', 'None')}")
            print()
        
        # Test deletion matching logic
        if enhanced_predictions and main_predictions:
            print("4️⃣ Testing Deletion Matching:")
            
            # Take the first main prediction and see if it would match enhanced predictions
            main_pred = main_predictions[0] if main_predictions else None
            if main_pred:
                print(f"   Testing deletion of main prediction:")
                print(f"     Score: {main_pred.score}")
                print(f"     Set: {main_pred.set_number}")
                print(f"     Timestamp: {main_pred.timestamp}")
                
                # Check if any enhanced predictions would match
                from datetime import datetime
                target_timestamp = datetime.fromisoformat(main_pred.timestamp.replace('Z', '+00:00'))
                
                matches = []
                for enhanced_pred in enhanced_predictions:
                    try:
                        enhanced_timestamp = datetime.fromisoformat(enhanced_pred.timestamp.replace('Z', '+00:00'))
                        time_diff = abs((enhanced_timestamp - target_timestamp).total_seconds())
                        
                        is_match = (
                            enhanced_pred.score == main_pred.score and
                            enhanced_pred.set_number == main_pred.set_number and
                            time_diff <= 60  # 60 second tolerance
                        )
                        
                        if is_match:
                            matches.append((enhanced_pred, time_diff))
                            
                    except Exception as e:
                        print(f"     Error checking enhanced prediction: {e}")
                
                print(f"   Found {len(matches)} matching enhanced predictions")
                for enhanced_pred, time_diff in matches:
                    print(f"     Match: {enhanced_pred.score} at {enhanced_pred.timestamp} (diff: {time_diff}s)")
            
        return True
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_deletion_issue()
